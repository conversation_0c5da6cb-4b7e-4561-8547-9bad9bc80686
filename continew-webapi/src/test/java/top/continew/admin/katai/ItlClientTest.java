package top.continew.admin.katai;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.strategy.impl.ItlOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Interlace卡台客户端测试类
 *
 * @version: 1.00.00
 * @description: Interlace卡台测试
 * @date: 2025/3/13 14:23
 */
@SpringBootTest
@Slf4j
public class ItlClientTest {
    @Resource
    private ItlOpsStrategyImpl itlOpsStrategy;
    @Resource
    private CardService cardService;
    @Resource
    private CardTransactionService cardTransactionService;


    @Test
    public void testLoadCard() {
        cardService.syncData(10);
        cardService.loadCardSensitiveInfo(CardPlatformEnum.INTERLACE);
    }

    @Test
    public void testOpenCard() {
        JSONObject data = new JSONObject();
        data.put("cardBin", "49387520");
        data.put("platformAdId", "123456");
        itlOpsStrategy.openCard(data);

    }

    @Test
    public void testGetTransactionList() {
        LocalDateTime end = LocalDateTimeUtil.now();
        LocalDateTime start = end.minusDays(30);
//        List<CardTransactionDO> list = itlOpsStrategy.getCardTransactionList(start, end, null);
//        for (CardTransactionDO cardTransactionDO : list) {
//            log.info("{}", JSON.toJSONString(cardTransactionDO));
//        }

        cardTransactionService.syncData(CardPlatformEnum.INTERLACE, start, end);

    }

    @Test
    public void testGetCardBalanceList() {
        LocalDateTime end = LocalDateTimeUtil.now();
        LocalDateTime start = end.minusDays(7);
        List<CardBalanceDO> list = itlOpsStrategy.getCardBalanceList(start, end);
        for (CardBalanceDO cardBalanceDO : list) {
            log.info("cardBalance:{}-{}-{}", cardBalanceDO.getCardNumber(), cardBalanceDO.getAmount(), cardBalanceDO.getAfterAmount());
        }
    }

    @Test
    public void testGetCardList() {
        //card:{"association":"VISA","balance":0,"cardNumber":"49387520****1268","openTime":"2025-08-14T14:53:31.571","platform":"INTERLACE","platformCardId":"8084513a-7f1c-46cc-8a62-36ce29138554","remark":"123456","status":"LOCKED","usedAmount":0}
        //card detail:{"association":"VISA","balance":0,"cardNumber":"49387520****1268","openTime":"2025-08-14T14:53:31.571","platform":"INTERLACE","platformCardId":"8084513a-7f1c-46cc-8a62-36ce29138554","remark":"123456","status":"LOCKED","usedAmount":0},CardDO(super=top.continew.admin.biz.model.entity.CardDO@d5a39438, cardNumber=49387520****1268, platform=INTERLACE, balance=0, status=LOCKED, openTime=2025-08-14T14:53:31.571, platformCardId=8084513a-7f1c-46cc-8a62-36ce29138554, expireDate=null, association=VISA, createTime=null, createUser=null, updateTime=null, remark=123456, usedAmount=0, cardName=null, cvv=null, platformAdId=null, platformCardHolderId=null, adPlatform=null)

        List<CardDO> list = itlOpsStrategy.getCardList(null, null, null);
        for (CardDO cardDO : list) {
            log.info("card:{}", JSON.toJSONString(cardDO));
            
            log.info("card detail:{},{}", JSON.toJSONString(cardDO), itlOpsStrategy.getCardDetail(cardDO.getPlatformCardId()));
        }
    }

    @Test
    public void testGetCardDetail() {
        CardDO card = itlOpsStrategy.getCardDetail("42c3bedc-482e-4754-9302-9f79ff279309");
        log.info("cardNumber:{}", card.getCardNumber());
    }

    @Test
    public void testGetCardBinList() {
        List<LabelValueResp<String>> list = cardService.getCardBinList(CardPlatformEnum.INTERLACE);
        
        for (LabelValueResp<String> stringLabelValueResp : list) {
            log.info("label:{}", stringLabelValueResp.getLabel());
        }
    }

    @Test
    public void testGetCardSensitiveDetail() {
        CardDO sensitiveDetail = itlOpsStrategy.getCardSensitiveDetail("42c3bedc-482e-4754-9302-9f79ff279309");
        log.info("sensitive detail:{}", JSON.toJSONString(sensitiveDetail));
    }

    @Test
    public void testGetCurrentBalance() {
        BigDecimal balance = itlOpsStrategy.getCurrentBalance();
        log.info("current balance:{}", balance);
    }

    @Test
    public void testActiveCard() {
        CardDO card = new CardDO();
        card.setPlatformCardId("42c3bedc-482e-4754-9302-9f79ff279309");
        itlOpsStrategy.activeCard(card);
        log.info("card activated successfully");

        card = itlOpsStrategy.getCardDetail("42c3bedc-482e-4754-9302-9f79ff279309");
        log.info("cardNumber:{}", card.getCardNumber());
    }

    @Test
    public void testInactiveCard() {
        CardDO card = new CardDO();
        card.setPlatformCardId("42c3bedc-482e-4754-9302-9f79ff279309");
        itlOpsStrategy.inactiveCard(card);
        log.info("card inactivated successfully");

        card = itlOpsStrategy.getCardDetail("42c3bedc-482e-4754-9302-9f79ff279309");
        log.info("cardNumber:{}", card.getCardNumber());
    }

    @Test
    public void testGetCardBalance() {
        CardDO card = new CardDO();
        card.setPlatformCardId("test_card_id");
        BigDecimal balance = itlOpsStrategy.getCardBalance(card);
        log.info("card balance:{}", balance);
    }

    @Test
    public void recharge() {
        CardDO card = new CardDO();
        card.setPlatformCardId("5e98a4de-e953-48f9-bdfc-185114ce7524");
        itlOpsStrategy.rechargeCard(card, BigDecimal.valueOf(7));

        card = itlOpsStrategy.getCardDetail("5e98a4de-e953-48f9-bdfc-185114ce7524");
        log.info("card:{}", card);
    }



    @Test
    public void withdraw() {
        CardDO card = new CardDO();
        card.setPlatformCardId("5e98a4de-e953-48f9-bdfc-185114ce7524");
        itlOpsStrategy.withdrawCard(card, null);

        card = itlOpsStrategy.getCardDetail("5e98a4de-e953-48f9-bdfc-185114ce7524");
        log.info("card:{}", card);
    }

    /*
    curl --location 'https://staging-assets.qbitnetwork.com/api/simulate/card/auth' \
    --header 'Content-Type: application/json' \
    -H 'x-access-token: d6583df1a6b6035535fcb14aeb09ee0d4ef595df' \
    --data '{
        "cardId": "5e98a4de-e953-48f9-bdfc-185114ce7524",
        "amount": "8"
    }'
     */
}