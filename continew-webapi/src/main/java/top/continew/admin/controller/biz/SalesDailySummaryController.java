package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.SalesDailySummaryQuery;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.model.resp.SalesDailySummaryDetailResp;
import top.continew.admin.biz.model.resp.SalesDailySummaryResp;
import top.continew.admin.biz.service.SalesDailySummaryService;

/**
 * 商务日报管理 API
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Tag(name = "商务日报管理 API")
@RestController
@CrudRequestMapping(value = "/biz/salesDailySummary", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class SalesDailySummaryController extends BaseController<SalesDailySummaryService, SalesDailySummaryResp, SalesDailySummaryDetailResp, SalesDailySummaryQuery, SalesDailySummaryReq> {

}