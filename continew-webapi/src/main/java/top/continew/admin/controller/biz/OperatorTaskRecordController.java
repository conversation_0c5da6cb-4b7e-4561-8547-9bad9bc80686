package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;
import top.continew.admin.biz.service.OperatorTaskRecordService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 运营人员工作记录管理 API
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Tag(name = "运营人员工作记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/operatorTaskRecord", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class OperatorTaskRecordController extends BaseController<OperatorTaskRecordService, OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> {

    @Override
    public BasePageResp<OperatorTaskRecordResp> page(OperatorTaskRecordQuery query, PageQuery pageQuery) {
        return super.page(query, pageQuery);
    }

    @GetMapping("/summaryStat")
    @Operation(summary = "工作登记数据汇总", description = "工作登记数据汇总")
    public List<OperatorTaskSummaryStatResp> summaryStat(OperatorTaskRecordQuery query) {
        return  this.baseService.summaryStat(query);
    }

    @Override
    public void export(OperatorTaskRecordQuery query, SortQuery sortQuery, HttpServletResponse response) {
        this.baseService.export(query, sortQuery, response);
    }
}