package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;
import top.continew.admin.biz.service.SalesScheduleService;

/**
 * 商务人员排班/请假记录管理 API
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Tag(name = "商务人员排班/请假记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/salesSchedule", api = {Api.PAGE, Api.EXPORT,Api.ADD,Api.DELETE})
public class SalesScheduleController extends BaseController<SalesScheduleService, SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq> {}