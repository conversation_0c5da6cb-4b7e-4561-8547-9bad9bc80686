package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.AdProductStatItemQuery;
import top.continew.admin.biz.model.req.AdProductStatItemReq;
import top.continew.admin.biz.model.resp.AdProductStatItemDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatItemResp;
import top.continew.admin.biz.service.AdProductStatItemService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 投放日报明细管理 API
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@SaIgnore
@Tag(name = "投放日报明细管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adProductStatItem", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class AdProductStatItemController extends BaseController<AdProductStatItemService, AdProductStatItemResp, AdProductStatItemDetailResp, AdProductStatItemQuery, AdProductStatItemReq> {}