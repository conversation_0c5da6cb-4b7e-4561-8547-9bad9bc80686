package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.OperationsRecordStatQuery;
import top.continew.admin.biz.model.query.OperationsStatQuery;
import top.continew.admin.biz.model.resp.OperationsStatResp;
import top.continew.admin.biz.model.resp.operationTaskStatResp;
import top.continew.admin.biz.service.OperationsStatService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

@RestController
@RequiredArgsConstructor
@RequestMapping("/biz/operationsStat")
public class OperationsStatController {

    private final OperationsStatService operationsStatService;

    @GetMapping("/page")
    public PageResp<OperationsStatResp> page(OperationsStatQuery query, PageQuery pageQuery) {
        return operationsStatService.page(query,pageQuery);
    }

    @GetMapping("/operationTaskStat")
    @Operation(summary = "工作记录统计", description = "工作记录统计页面")
    public PageResp<operationTaskStatResp> operationTaskStatPage(OperationsRecordStatQuery query, PageQuery pageQuery) {
        return operationsStatService.operationTaskStatPage(query,pageQuery);
    }
}
