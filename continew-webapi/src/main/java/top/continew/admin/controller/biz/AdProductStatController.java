package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.AdProductStatQuery;
import top.continew.admin.biz.model.query.CampaignDataQuery;
import top.continew.admin.biz.model.req.AdProductStatReq;
import top.continew.admin.biz.model.resp.AdProductStatDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatResp;
import top.continew.admin.biz.model.resp.CampaignPerformanceResp;
import top.continew.admin.biz.service.AdProductStatService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import java.util.List;

/**
 * 产品日报管理 API
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Tag(name = "产品日报管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adProductStat", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class AdProductStatController extends BaseController<AdProductStatService, AdProductStatResp, AdProductStatDetailResp, AdProductStatQuery, AdProductStatReq> {

    @GetMapping("/campaignData")
    public List<CampaignPerformanceResp> campaignData(@Validated CampaignDataQuery query) {
        return baseService.campaignData(query);
    }

}