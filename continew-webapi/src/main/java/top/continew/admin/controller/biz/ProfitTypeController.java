package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.ProfitTypeQuery;
import top.continew.admin.biz.model.req.ProfitTypeReq;
import top.continew.admin.biz.model.resp.ProfitTypeDetailResp;
import top.continew.admin.biz.model.resp.ProfitTypeResp;
import top.continew.admin.biz.service.ProfitTypeService;

/**
 * 利润类型管理 API
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Tag(name = "利润类型管理 API")
@RestController
@CrudRequestMapping(value = "/biz/profitType", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class ProfitTypeController extends BaseController<ProfitTypeService, ProfitTypeResp, ProfitTypeDetailResp, ProfitTypeQuery, ProfitTypeReq> {}