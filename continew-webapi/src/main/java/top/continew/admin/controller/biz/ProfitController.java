package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.ProfitQuery;
import top.continew.admin.biz.model.req.ProfitReq;
import top.continew.admin.biz.model.resp.ProfitDetailResp;
import top.continew.admin.biz.model.resp.ProfitResp;
import top.continew.admin.biz.service.ProfitService;

/**
 * 利润管理 API
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Tag(name = "利润管理 API")
@RestController
@CrudRequestMapping(value = "/biz/profit", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class ProfitController extends BaseController<ProfitService, ProfitResp, ProfitDetailResp, ProfitQuery, ProfitReq> {}