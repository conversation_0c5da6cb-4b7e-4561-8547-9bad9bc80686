package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.AdProductQuery;
import top.continew.admin.biz.model.req.AdProductReq;
import top.continew.admin.biz.model.resp.AdProductDetailResp;
import top.continew.admin.biz.model.resp.AdProductResp;
import top.continew.admin.biz.service.AdProductService;

/**
 * 投放产品管理 API
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Tag(name = "投放产品管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adProduct", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class AdProductController extends BaseController<AdProductService, AdProductResp, AdProductDetailResp, AdProductQuery, AdProductReq> {}