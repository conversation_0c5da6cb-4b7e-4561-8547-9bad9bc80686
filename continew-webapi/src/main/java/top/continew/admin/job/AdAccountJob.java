/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.AdAccountKeepStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.req.IdsReq;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.ThreadPoolHelper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@RequiredArgsConstructor
@Slf4j
public class AdAccountJob {

    private final AdAccountInsightService adAccountInsightService;

    private final AdAccountService adAccountService;

    private final AdAccountTransactionService adAccountTransactionService;

    private final BusinessManagerService businessManagerService;

    private final ThreadPoolExecutor threadPoolExecutor = ThreadPoolHelper.getOrderInstance();

    private final AdAccountSyncService adAccountSyncService;

    private final PersonalAccountService personalAccountService;
    private final AdAccountEffectiveSyncService adAccountEffectiveSyncService;


    /**
     * 广告成效数据的同步
     * @param jobArgs
     */
    @JobExecutor(name = "syncAdAccountEffectiveStatData")
    public void syncAdAccountEffectiveStatData(JobArgs jobArgs) {
        LocalDate startTime = null;
        LocalDate endTime = null;
        
        try {
            if (jobArgs.getJobParams() != null) {
                String paramsStr = (String)jobArgs.getJobParams();
                List<String> paramsSplit = StrUtil.split(paramsStr, "|", true, true);
                if (!paramsSplit.isEmpty()) {
                    if (paramsSplit.size() > 1) {
                        startTime = LocalDateTimeUtil.parseDate(paramsSplit.get(1), "yyyy-MM-dd");
                        endTime = LocalDateTimeUtil.parseDate(paramsSplit.get(2), "yyyy-MM-dd");
                    }
                }
            }
            
            LocalDateTime now = LocalDateTime.now();
            if (startTime == null || endTime == null) {
                endTime = now.toLocalDate();
                startTime = now.minusDays(7).toLocalDate();
            }
            
            LocalDate finalStartTime = startTime;
            LocalDate finalEndTime = endTime;
            
            List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                    .eq(PersonalAccountDO::getIsSync, true));
            for (PersonalAccountDO personalAccountDO : observeAccountList) {
                threadPoolExecutor.execute(() -> {
                    try {
                        // 拉取指定时间范围的数据
                        adAccountEffectiveSyncService.syncAdSetsEffectiveData(personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), true, finalStartTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), finalEndTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        
                    } catch (Exception e) {
                        log.error("同步广告成效数据失败，账户: {}, 时间范围: {} 至 {}", personalAccountDO.getBrowserNo(), finalStartTime, finalEndTime, e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("同步广告成效数据任务执行失败", e);
        }
    }


    @JobExecutor(name = "syncAdAccountStatData")
    public void syncAdAccountStatData(JobArgs jobArgs) {
        LocalDate startTime = null;
        LocalDate endTime = null;
        if (jobArgs.getJobParams() == null) {
            return;
        }
        String paramsStr = (String)jobArgs.getJobParams();
        List<String> paramsSplit = StrUtil.split(paramsStr, "|", true, true);
        if (paramsSplit.isEmpty()) {
            return;
        }
        if (paramsSplit.size() > 1) {
            startTime = LocalDateTimeUtil.parseDate(paramsSplit.get(1), "yyyy-MM-dd");
            endTime = LocalDateTimeUtil.parseDate(paramsSplit.get(2), "yyyy-MM-dd");
        }
        LocalDateTime now = LocalDateTime.now();
        if (startTime == null || endTime == null) {
            endTime = now.toLocalDate();
            startTime = now.minusDays(7).toLocalDate();
        }
        List<String> browsers = StrUtil.split(paramsSplit.get(0), ",", true, true);
        LocalDate finalStartTime = startTime;
        LocalDate finalEndTime = endTime;
        //        for (String s : browsers) {
        //            threadPoolExecutor.execute(() -> adAccountInsightService.syncAdAccountStatData(s, null, null, null, finalStartTime, finalEndTime));
        //        }
        List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true));
        for (PersonalAccountDO personalAccountDO : observeAccountList) {
            threadPoolExecutor.execute(() -> adAccountInsightService.syncAdAccountStatData(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), finalStartTime, finalEndTime));
        }
    }

    @JobExecutor(name = "syncAdAccountStatDataManual")
    public void syncAdAccountStatManualData(JobArgs jobArgs) {
        String params = (String)jobArgs.getJobParams();
        List<String> accounts = StrUtil.split(params, "\n", true, true);
        for (String account : accounts) {
            List<String> splits = StrUtil.split(account, "|", true, true);
            PersonalAccountDO personalAccount = personalAccountService.getOne(Wrappers.<PersonalAccountDO>lambdaQuery()
                .eq(PersonalAccountDO::getBrowserNo, splits.get(0)));
            if (personalAccount == null || StringUtils.isBlank(personalAccount.getHeaders())) {
                continue;
            }
            LocalDate startTime = LocalDateTimeUtil.parseDate(splits.get(1), "yyyy-MM-dd");
            LocalDate endTime = LocalDateTimeUtil.parseDate(splits.get(2), "yyyy-MM-dd");
            adAccountInsightService.syncAdAccountStatData(null, personalAccount.getBrowserNo(), personalAccount.getProxy(), personalAccount.getHeaders(), startTime, endTime);
        }
    }

    @JobExecutor(name = "getAllSpentJob")
    public void getAllSpent(JobArgs jobArgs) {
        List<String> browserNoList = new ArrayList<>();
        if (jobArgs.getJobParams() != null && StringUtils.isNotBlank((String)jobArgs.getJobParams())) {
            browserNoList = StrUtil.split((String) jobArgs.getJobParams(), ",", true, true);
        }
        boolean isLite = LocalDateTime.now().getMinute() >= 10;
        List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true)
            .in(!browserNoList.isEmpty(), PersonalAccountDO::getBrowserNo, browserNoList));
        for (PersonalAccountDO personalAccountDO : observeAccountList) {
            if (personalAccountDO.getBrowserNo().equals("34307")) {
                threadPoolExecutor.execute(() -> adAccountService.syncAllSpend(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), true));
            } else {
                threadPoolExecutor.execute(() -> adAccountService.syncAllSpend(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), isLite));
            }
        }
    }

    @JobExecutor(name = "getPaymentTransactionJob")
    public void getPaymentTransaction(JobArgs jobArgs) {
        if (jobArgs.getJobParams() == null) {
            return;
        }
        String params = (String)jobArgs.getJobParams();
        List<String> paramsSplit = StrUtil.split(params, "|", true, true);
        if (paramsSplit.isEmpty()) {
            return;
        }
        boolean isLite;
        List<String> obsList = StrUtil.split(paramsSplit.get(0), ",", true, true);
        if (paramsSplit.size() > 1) {
            isLite = Objects.equals(paramsSplit.get(1), "1");
        } else {
            isLite = true;
        }
        //        for (String s : obsList) {
        //            threadPoolExecutor.execute(() -> adAccountTransactionService.syncAdAccountBill(s, null, null, null, isLite));
        //        }
        List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true));
        for (PersonalAccountDO personalAccountDO : observeAccountList) {
            threadPoolExecutor.execute(() -> adAccountTransactionService.syncAdAccountBill(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), isLite));
        }
    }

    @JobExecutor(name = "checkAmountSpentJob")
    public void checkAmountSpentJob() {
        List<AdAccountDO> adAccountList = adAccountService.list(Wrappers.<AdAccountDO>lambdaQuery()
            .in(AdAccountDO::getKeepStatus, List.of(AdAccountKeepStatusEnum.PROCESS, AdAccountKeepStatusEnum.OBSERVE, AdAccountKeepStatusEnum.WAIT_SPENT))
            .eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL));
        for (AdAccountDO adAccountDO : adAccountList) {
            if (adAccountDO.getAmountSpent() != null && adAccountDO.getAmountSpent()
                .compareTo(new BigDecimal(4)) >= 0) {
                log.info("【养号花费检测】{}当前消耗：{}，养号成功", adAccountDO.getId(), adAccountDO.getAmountSpent());
                adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                    .set(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.FINISH)
                    .eq(AdAccountDO::getId, adAccountDO.getId()));
            }
        }
    }

    @JobExecutor(name = "calcAdAccountCostJob")
    public void calcAdAccountCost() {
        LocalDate yesterday = LocalDateTime.now().minusDays(1).toLocalDate();
        businessManagerService.calcAdAccountCost(yesterday);
    }

    @JobExecutor(name = "checkAdAccountNurturingStatusJob")
    public void checkAdAccountNurturingStatus() {
        adAccountService.checkAdAccountNurturingStatus(new IdsReq());
    }

    @JobExecutor(name = "getAllAdInfos")
    public void getAllAdInfos() {
        ThreadPoolExecutor executor = ThreadPoolHelper.getAdAccountInfoInstance();
        List<PersonalAccountDO> list = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true));
        for (PersonalAccountDO personalAccountDO : list) {
            executor.execute(() -> adAccountSyncService.getAllAdInfos(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), true));
        }
    }
}
