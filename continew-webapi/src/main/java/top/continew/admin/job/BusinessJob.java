package top.continew.admin.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.mapper.ClearOrderMapper;
import top.continew.admin.biz.mapper.RechargeOrderMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.BusinessStatisticsQuery;
import top.continew.admin.biz.model.resp.BusinessStatisticsResp;
import top.continew.admin.biz.model.resp.UserOrderStatResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.CustomerStatisticsService;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessJob {

    private final CustomerStatisticsService customerStatisticsService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final CustomerService customerService;

    private final RechargeOrderMapper rechargeOrderMapper;

    private final ClearOrderMapper clearOrderMapper;

    private final AdAccountOrderMapper adAccountOrderMapper;

    @JobExecutor(name = "sendBusinessStatDataJob")
    public void sendBusinessStatDataJob() {
        // 每日数据发送
        LocalDateTime[] yesterday = getStatTimes("yesterday");
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getBusinessChatId())
            .text(this.buildMsg(yesterday, getBusinessStatData(yesterday)))
            .build()));
        LocalDateTime now = LocalDateTime.now();
        if (now.getDayOfWeek() == DayOfWeek.MONDAY) {
            LocalDateTime[] lastWeek = getStatTimes("lastWeek");
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getBusinessChatId())
                .text(this.buildMsg(lastWeek, getBusinessStatData(lastWeek)))
                .build()));
        }
        if (now.getDayOfMonth() == 1) {
            LocalDateTime[] lastMonth = getStatTimes("lastMonth");
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getBusinessChatId())
                .text(this.buildMsg(lastMonth, getBusinessStatData(lastMonth)))
                .build()));
        }
    }

    /**
     * 客户实消预警
     */
    @JobExecutor(name = "customerSpentWarnJob")
    public void customerSpentWarn() {
        List<CustomerDO> customerList = customerService.list(Wrappers.<CustomerDO>lambdaQuery()
            .eq(CustomerDO::getSettleType, CustomerSettleTypeEnum.TWO));
        for (CustomerDO customerDO : customerList) {
            BigDecimal fbBalance = adAccountOrderMapper.getCustomerFbBalance(customerDO.getId());
            BigDecimal lastBalance = fbBalance.add(customerDO.getBalance());
            log.info("【实消余额预警】{} 当前占用资金：{}，预警值：{}", customerDO.getName(), lastBalance, customerDO.getWarnLimitAmount());
            if (lastBalance.compareTo(customerDO.getWarnLimitAmount()) < 0) {
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getBusinessChatId())
                    .text("【实消额度预警】客户 %s 当前剩余可用额度 %s,已低于预警值 %s，请及时催款".formatted(customerDO.getName(), NumberUtil.toStr(lastBalance), NumberUtil.toStr(customerDO.getWarnLimitAmount())))
                    .build()));
                if (customerDO.getDebtNotify()) {
                    SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                        .chatId(customerDO.getTelegramChatId())
                        .text("当前账户剩余可用额度 %s，请及时补款。当额度透支时，系统将进行冻结操作".formatted(NumberUtil.toStr(lastBalance)))
                        .build()));
                }
            }
        }
    }

    @JobExecutor(name = "sendUserOrderStatJob")
    public void sendUserOrderStat() {
        // 每日数据发送
        LocalDateTime[] yesterday = getStatTimes("yesterday");
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getOperationChatId())
            .text(this.getOperationStatData(yesterday))
            .build()));
        LocalDateTime now = LocalDateTime.now();
        if (now.getDayOfWeek() == DayOfWeek.MONDAY) {
            LocalDateTime[] lastWeek = getStatTimes("lastWeek");
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getOperationChatId())
                .text(this.getOperationStatData(lastWeek))
                .build()));
        }
        if (now.getDayOfMonth() == 1) {
            LocalDateTime[] lastMonth = getStatTimes("lastMonth");
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getOperationChatId())
                .text(this.getOperationStatData(lastMonth))
                .build()));
        }
    }

    private List<BusinessStatisticsResp> getBusinessStatData(LocalDateTime[] statTimes) {
        BusinessStatisticsQuery query = new BusinessStatisticsQuery();
        query.setStatTimes(statTimes);
        return customerStatisticsService.listBusinessData(query);
    }

    private String getOperationStatData(LocalDateTime[] statTimes) {
        List<UserOrderStatResp> rechargeList = rechargeOrderMapper.selectUserOrderStat(statTimes[0], statTimes[1]);
        List<UserOrderStatResp> clearList = clearOrderMapper.selectUserOrderStat(statTimes[0], statTimes[1]);
        List<UserOrderStatResp> adAccountOrderList = adAccountOrderMapper.selectUserOrderStat(statTimes[0], statTimes[1]);
        StringBuilder string = new StringBuilder();
        string.append("总充值笔数：")
            .append(rechargeList.stream().mapToInt(UserOrderStatResp::getNum).sum())
            .append("\n\n");
        for (UserOrderStatResp userOrderStatResp : rechargeList) {
            string.append(userOrderStatResp.getNickname())
                .append("充值笔数：")
                .append(userOrderStatResp.getNum())
                .append("\n");
        }
        string.append("\n");
        string.append("总清零笔数：")
            .append(clearList.stream().mapToInt(UserOrderStatResp::getNum).sum())
            .append("\n\n");
        for (UserOrderStatResp userOrderStatResp : clearList) {
            string.append(userOrderStatResp.getNickname())
                .append("清零笔数：")
                .append(userOrderStatResp.getNum())
                .append("\n");
        }
        string.append("\n");
        string.append("总下户数：")
            .append(adAccountOrderList.stream().mapToInt(UserOrderStatResp::getNum).sum())
            .append("\n\n");
        for (UserOrderStatResp userOrderStatResp : adAccountOrderList) {
            string.append(userOrderStatResp.getNickname())
                .append("下户数：")
                .append(userOrderStatResp.getNum())
                .append("\n");
        }
        return string.toString();
    }

    private LocalDateTime[] getStatTimes(String dateRange) {
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        LocalDateTime now = LocalDateTime.now();
        switch (dateRange) {
            case "yesterday" -> {
                startTime = LocalDate.now().minusDays(1).atStartOfDay();
                endTime = startTime.withHour(23).withMinute(59).withSecond(59);
            }
            case "lastWeek" -> {
                startTime = now.minusWeeks(1).with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
                endTime = startTime.plusDays(6).withHour(23).withMinute(59).withSecond(59);
            }
            case "lastMonth" -> {
                startTime = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
                endTime = startTime.plusMonths(1).minusSeconds(1);
            }
            default -> {
                throw new BusinessException("暂无支持的日期统计范围");
            }
        }
        return new LocalDateTime[] {startTime, endTime};
    }

    private String buildMsg(LocalDateTime[] statTimes, List<BusinessStatisticsResp> data) {
        StringBuilder msg = new StringBuilder();
        String splitStr = " | ";
        msg.append(LocalDateTimeUtil.format(statTimes[0], "yyyy-MM-dd"))
            .append("~")
            .append(LocalDateTimeUtil.format(statTimes[1], "yyyy-MM-dd"))
            .append("\n")
            .append("商务 | 充值 | 消耗 | 新增客户")
            .append("\n");
        for (BusinessStatisticsResp datum : data) {
            msg.append(datum.getNickname())
                .append(splitStr)
                .append(datum.getTotalPaid())
                .append(splitStr)
                .append(datum.getTotalSpend())
                .append(splitStr)
                .append(datum.getTotalNewCustomer())
                .append("\n\n");
        }
        return msg.toString();
    }
}
