package top.continew.admin.system.model.req.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "Telegram重置参数")
public class UserTelegramResetReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "飞机号")
    private String telegramId;

}
