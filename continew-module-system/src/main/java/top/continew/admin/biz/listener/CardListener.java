package top.continew.admin.biz.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.event.CardTransactionEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.starter.cache.redisson.util.RedisUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Component
@Slf4j
@RequiredArgsConstructor
public class CardListener {

    private final String UNUSUAL_REDIS_KEY = "ad_account_transaction_unusual:";

    private final String FAIL_REDIS_KEY = "ad_account_transaction_fail:";

    private static final int MAX_PAYMENTS = 5; // 最大支付次数

    private static final Duration TIME_WINDOW = Duration.ofMinutes(10); // 时间窗口

    private final List<CardTransactionDO> transactionHistory = new CopyOnWriteArrayList<>();

    private final AdAccountService adAccountService;

    private final CardService cardService;

    private final AdAccountCardService adAccountCardService;

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final AdAccountOrderMapper adAccountOrderMapper;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final CardTransactionService cardTransactionService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Async
    @EventListener
    public void transaction(CardTransactionEvent event) {
        CardTransactionDO cardTransactionDO = (CardTransactionDO)event.getSource();
        if (StringUtils.isBlank(cardTransactionDO.getCardNumber())) {
            return;
        }
        boolean needCheck = false;
        if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION)) {
            needCheck = true;
        } else if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION_BACK)) {
            // 短时间预扣退回，排除一刀流户
            long minutes = LocalDateTimeUtil.between(cardTransactionDO.getStatTime(), cardTransactionDO.getChinaTime(), ChronoUnit.MINUTES);
            if (minutes < 5) {
                needCheck = true;
            }
        }
        if (!needCheck) {
            return;
        }
        CardDO card = cardService.getByCardNumberByCache(cardTransactionDO.getCardNumber());
        if (card == null || StringUtils.isBlank(card.getPlatformAdId())) {
            return;
        }

        // 判断广告户状态
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(card.getPlatformAdId());
        if (adAccount == null || adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.INVALID)) {
            return;
        }
        if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION_BACK)) {
            this.checkSaltAdAccountTransactionRevokeTooFast(cardTransactionDO, card, adAccount);
            return;
        }
        this.checkAdAccountTransExcessive(cardTransactionDO, card, adAccount);
        this.checkSaltAdAccountTransFailed(cardTransactionDO, card, adAccount);
        this.checkAdAccountTransUnusual(cardTransactionDO, card, adAccount);
        this.autoRecharge(cardTransactionDO, card, adAccount);
    }

    /**
     * 检测是否回退过快
     *
     * @param cardTransactionDO
     * @param card
     * @param adAccount
     */
    private void checkSaltAdAccountTransactionRevokeTooFast(CardTransactionDO cardTransactionDO,
                                                            CardDO card,
                                                            AdAccountDO adAccount) {
        if (adAccount == null || !adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.SALT) || adAccount.getAccountStatus()
            .equals(AdAccountStatusEnum.BANNED) || !adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT)) {
            return;
        }
        AdAccountOrderDO accountOrderDO = adAccountOrderMapper.selectOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getAdAccountId, adAccount.getPlatformAdId())
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        if (!adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT)) {
            return;
        }
        BigDecimal revokeAmount = BigDecimal.ZERO;
        if (accountOrderDO.getIsOneDollar()) {
            CardTransactionDO originTransaction = cardTransactionService.getOne(Wrappers.<CardTransactionDO>lambdaQuery()
                .eq(CardTransactionDO::getOriginTransactionId, cardTransactionDO.getOriginTransactionId()));
            if (originTransaction != null) {
                revokeAmount = originTransaction.getTransAmount().abs();
                if (revokeAmount.compareTo(new BigDecimal(10)) < 0) {
                    return;
                }
            }
        }
        String customerName = adAccountOrderMapper.getCustomerName(adAccount.getPlatformAdId());
        String text = BotUtils.createUnSaltAdAccountTransAmountRevokeTooFastMessage(adAccount.getPlatformAdId(), customerName, revokeAmount);
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getMonitorChatId())
            .text(text)
            .parseMode(ParseMode.MARKDOWNV2)
            .build()));
    }

    /**
     * 检查已出售广告户扣款失败问题
     *
     * @param cardTransaction
     * @param card
     * @param adAccount
     */
    private void checkSaltAdAccountTransFailed(CardTransactionDO cardTransaction, CardDO card, AdAccountDO adAccount) {
        if (!cardTransaction.getTransStatus().equals(CardTransactionStatusEnum.FAIL)) {
            return;
        }
        if (adAccount == null || !adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.SALT) || adAccount.getAccountStatus()
            .equals(AdAccountStatusEnum.BANNED) || !adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT)) {
            return;
        }
        if (RedisUtils.exists(FAIL_REDIS_KEY + adAccount.getPlatformAdId())) {
            return;
        }
        String failureReason = StringUtils.defaultIfBlank(cardTransaction.getRemark(), cardTransaction.getTransDetail());
        BigDecimal cardBalance = cardOpsStrategyFactory.findStrategy(card.getPlatform()).getCardBalance(card);
        String customerName = adAccountOrderMapper.getCustomerName(adAccount.getPlatformAdId());
        String text = BotUtils.createSaltAdAccountPayFailedMessage(adAccount.getPlatformAdId(), customerName, card.getCardNumber(), failureReason, NumberUtil.toStr(cardTransaction.getTransAmount()
            .abs()), NumberUtil.toStr(adAccount.getSpendCap()
            .subtract(adAccount.getAmountSpent())), NumberUtil.toStr(cardBalance));
        RedisUtils.set(FAIL_REDIS_KEY + adAccount.getPlatformAdId(), "1", Duration.ofMinutes(10));
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getMonitorChatId())
            .text(text)
            .parseMode(ParseMode.MARKDOWNV2)
            .build()));
    }

    /**
     * 检测交易流水异常问题
     *
     * @param cardTransaction
     * @param card
     * @param adAccount
     */
    private void checkAdAccountTransUnusual(CardTransactionDO cardTransaction, CardDO card, AdAccountDO adAccount) {
        if (adAccount.getKeepStatus().equals(AdAccountKeepStatusEnum.LOGIN_SUCCESS) || adAccount.getAccountStatus()
            .equals(AdAccountStatusEnum.BANNED)) {
            return;
        }
        if (FacebookUtils.ignoreCardBalanceAdjustAdAccounts().contains(adAccount.getPlatformAdId())) {
            return;
        }
        // 检测账号的扣款金额是否过大异常
        if (cardTransaction.getTransAmount().compareTo(new BigDecimal(-200)) <= 0) {
            String text = BotUtils.createUnSaltAdAccountTransAmountUnusualMessage(adAccount.getPlatformAdId(), "扣款金额超出阈值200", cardTransaction.getTransAmount()
                .abs(), adAccount.getBillingThresholdCurrencyAmount());
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getMonitorChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getFinanceChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            AdAccountDO statusChange = new AdAccountDO();
            statusChange.setPlatformAdId(adAccount.getPlatformAdId());
            statusChange.setAccountStatus(AdAccountStatusEnum.BANNED);
            statusChange.setUsable(false);
            statusChange.setUnusableReason(AdAccountUnusableReasonEnum.BM_KICKED_BY_PERMISSION);
            AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
            SpringUtil.publishEvent(adAccountStatusChangeEvent);
        }
        if (!adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.SALT) && !adAccount.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.SALEING)) {
        } else {
            if (cardTransaction.getTransStatus()
                .equals(CardTransactionStatusEnum.PENDING) && cardTransaction.getTransAmount()
                .abs()
                .compareTo(new BigDecimal(100)) > 0) {
                String customerName = adAccountOrderMapper.getCustomerName(adAccount.getPlatformAdId());
                String text = BotUtils.createSaltAdAccountThresholdAmountTooLargeMessage(adAccount.getPlatformAdId(), customerName, "扣款金额大于100", cardTransaction.getTransAmount()
                    .abs(), adAccount.getBillingThresholdCurrencyAmount());
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
            }
            // 检测是否大于门槛金额
            if (adAccount.getBillingThresholdCurrencyAmount().compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            BigDecimal billingThreshold = adAccount.getBillingThresholdCurrencyAmount()
                .multiply(new BigDecimal("1.5"))
                .setScale(0, RoundingMode.UP);
            if (cardTransaction.getTransAmount().abs().compareTo(billingThreshold) > 0) {
                String customerName = adAccountOrderMapper.getCustomerName(adAccount.getPlatformAdId());
                String text = BotUtils.createSaltAdAccountTransAmountUnusualMessage(adAccount.getPlatformAdId(), customerName, "扣款金额大于广告户付费门槛", cardTransaction.getTransAmount()
                    .abs(), adAccount.getBillingThresholdCurrencyAmount());
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
            }
        }

    }

    /**
     * 检测交易频繁
     *
     * @param cardTransaction
     * @param card
     * @param adAccount
     */
    private void checkAdAccountTransExcessive(CardTransactionDO cardTransaction, CardDO card, AdAccountDO adAccount) {
        transactionHistory.add(cardTransaction);
        transactionHistory.removeIf(t -> Duration.between(t.getChinaTime(), LocalDateTime.now())
            .compareTo(TIME_WINDOW) > 0);
        // 检测是否短期内多次交易
        long currentTransactionCount = transactionHistory.stream()
            .filter(v -> v.getCardNumber().equals(cardTransaction.getCardNumber()))
            .count();
        if (currentTransactionCount > MAX_PAYMENTS && !RedisUtils.exists(UNUSUAL_REDIS_KEY + card.getPlatformAdId())) {
            List<CardTransactionDO> transactionDOS = transactionHistory.stream()
                .filter(v -> v.getCardNumber().equals(cardTransaction.getCardNumber()))
                .toList();
            String customerName = adAccountOrderMapper.getCustomerName(card.getPlatformAdId());
            String text = BotUtils.createAdAccountTransExcessiveMessage(card.getPlatformAdId(), customerName, "短时间出现多笔交易记录", CollectionUtil.sub(transactionDOS, 0, 5));
            RedisUtils.set(UNUSUAL_REDIS_KEY + card.getPlatformAdId(), "1", Duration.ofMinutes(60));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getMonitorChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
        }
        List<CardTransactionDO> greater100TransactionList = transactionHistory.stream()
            .filter(v -> v.getCardNumber().equals(cardTransaction.getCardNumber()) && v.getTransAmount()
                .compareTo(new BigDecimal(-100)) <= 0)
            .toList();
        // 短时间多笔金额大于100的交易
        if (greater100TransactionList.size() >= 4) {
            String customerName = adAccountOrderMapper.getCustomerName(card.getPlatformAdId());
            String text = BotUtils.createAdAccountTransExcessiveMessage(card.getPlatformAdId(), customerName, "短时间出现多笔大于100的交易记录", CollectionUtil.sub(greater100TransactionList, 0, 5));
            text += "如果误判，请前往广告户列表恢复可用。";
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getMonitorChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getFinanceChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            transactionHistory.removeIf(t -> t.getCardNumber().equals(cardTransaction.getCardNumber()));
            AdAccountDO statusChange = new AdAccountDO();
            statusChange.setPlatformAdId(adAccount.getPlatformAdId());
            statusChange.setAccountStatus(AdAccountStatusEnum.BANNED);
            statusChange.setUsable(false);
            statusChange.setUnusableReason(AdAccountUnusableReasonEnum.BM_KICKED_BY_PERMISSION);
            AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
            SpringUtil.publishEvent(adAccountStatusChangeEvent);
        }
    }

    /**
     * 自动补款策略
     *
     * @param cardTransaction
     * @param card
     * @param adAccount
     */
    private void autoRecharge(CardTransactionDO cardTransaction, CardDO card, AdAccountDO adAccount) {
        if (!cardTransaction.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION)) {
            return;
        }
        if (!cardTransaction.getTransStatus()
            .equals(CardTransactionStatusEnum.PENDING) && !cardTransaction.getTransStatus()
            .equals(CardTransactionStatusEnum.FAIL)) {
            return;
        }
        if (FacebookUtils.ignoreCardBalanceAdjustAdAccounts().contains(adAccount.getPlatformAdId())) {
            return;
        }
        if (!adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.SALT) || adAccount.getAccountStatus()
            .equals(AdAccountStatusEnum.BANNED)) {
            return;
        }
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(card.getPlatform());
        AdAccountOrderDO accountOrderDO = adAccountOrderMapper.selectOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getAdAccountId, adAccount.getPlatformAdId())
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        if (!adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT)) {
            return;
        }
        if (accountOrderDO.getIsOneDollar() && cardTransaction.getTransAmount().compareTo(new BigDecimal(-5)) >= 0) {
            if (cardTransaction.getTransStatus().equals(CardTransactionStatusEnum.FAIL)) {
                cardOpsStrategy.rechargeCard(card, new BigDecimal(300));
            }
            return;
        }
        if (!cardTransaction.getTransStatus().equals(CardTransactionStatusEnum.PENDING)) {
            return;
        }
        BigDecimal changeAmount = BigDecimal.ZERO;
        BigDecimal cardBalance = cardOpsStrategy.getCardBalance(card);
        BigDecimal fbBalance = adAccount.getSpendCap()
            .subtract(adAccount.getAmountSpent())
            .setScale(0, RoundingMode.UP);
        if (accountOrderDO.getEnablePrepay()) {
            // 预充户补钱规则
            BigDecimal totalAmount = new BigDecimal(100);
            if (cardBalance.compareTo(totalAmount) < 0) {
                changeAmount = totalAmount.subtract(cardBalance);
            } else if (cardBalance.compareTo(totalAmount) > 0) {
                changeAmount = totalAmount.subtract(cardBalance);
            }
        } else {
            if (adAccount.getBillingThresholdCurrencyAmount() == null || adAccount.getBillingThresholdCurrencyAmount()
                .compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }
            BigDecimal totalAmount = adAccount.getBillingThresholdCurrencyAmount().multiply(new BigDecimal(3));
            if (totalAmount.compareTo(new BigDecimal(50)) < 0) {
                totalAmount = new BigDecimal(50);
            }
            if (cardBalance.compareTo(totalAmount) < 0) {
                if (cardBalance.compareTo(adAccount.getBillingThresholdCurrencyAmount()
                    .multiply(BigDecimal.TEN)) <= 0 && cardBalance.compareTo(new BigDecimal(100)) <= 0) {
                    changeAmount = totalAmount.subtract(cardBalance);
                }
            } else if (cardBalance.compareTo(totalAmount) > 0) {
                changeAmount = totalAmount.subtract(cardBalance);
            }
            BigDecimal balanceNotifyLimit = new BigDecimal(50);
            if (fbBalance.compareTo(balanceNotifyLimit) <= 0) {
                String customerName = adAccountOrderMapper.getCustomerName(adAccount.getPlatformAdId());
                String text = BotUtils.createInsufficientBalanceMessage(adAccount.getPlatformAdId(), CommonUtils.escapeMarkdown(customerName), CommonUtils.escapeMarkdown(NumberUtil.toStr(fbBalance)));
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
            }
        }
        log.info("【卡片余额检测】广告户 {} 付费门槛：${}，卡片余额：${}", adAccount.getPlatformAdId(), adAccount.getBillingThresholdCurrencyAmount(), cardBalance);
        if (changeAmount.compareTo(BigDecimal.ZERO) != 0) {
            log.info("【卡片余额检测】广告户 {} 需{} ${}", adAccount.getPlatformAdId(), changeAmount.compareTo(BigDecimal.ZERO) > 0
                ? "充值"
                : "减款", changeAmount.abs());
            if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 计算账户补钱是否达到上限
                BigDecimal rechargeAmount = customerBalanceRecordService.getTotalRechargeAmount(accountOrderDO.getCustomerId(), accountOrderDO.getAdAccountId(), accountOrderDO.getPayTime());
                BigDecimal cardSpentAmount = cardTransactionService.getAdAccountCardSpent(null, accountOrderDO.getAdAccountId(), accountOrderDO.getFinishTime());
                BigDecimal lastAmount = rechargeAmount.subtract(cardSpentAmount);
                // 最多补充金额
                BigDecimal maxAddAmount = lastAmount.add(new BigDecimal(300));
                log.info("【卡片余额检测】广告户 {} fb余额：${}， 剩余充值额度：${}，最多可充值金额：${}", adAccount.getPlatformAdId(), fbBalance, lastAmount, maxAddAmount);
                if (maxAddAmount.compareTo(BigDecimal.ZERO) > 0) {
                    cardOpsStrategy.rechargeCard(card, NumberUtil.min(changeAmount, maxAddAmount));
                }
            } else {
                cardOpsStrategy.withdrawCard(card, changeAmount.abs());
            }
        }

    }
}
