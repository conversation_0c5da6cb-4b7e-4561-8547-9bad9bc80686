
package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.CustomerCycleCompareMapper;
import top.continew.admin.biz.model.query.CustomerCycleComparePeriodQuery;
import top.continew.admin.biz.model.query.CustomerCycleCompareSummaryQuery;
import top.continew.admin.biz.model.resp.CustomerCycleComparePeroidResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareBatchResp;
import top.continew.admin.biz.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户周期对比分析服务实现
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerCycleCompareServiceImpl implements CustomerCycleCompareService {
    private final CustomerCycleCompareMapper customerCycleCompareMapper;



    @Override
    public List<CustomerCycleCompareSummaryResp> listCustomerSummaryData(CustomerCycleCompareSummaryQuery query) {
        try {
            // 分别查询基础数据、空置户数据、消耗数据
            List<CustomerCycleCompareSummaryResp> basicDataList = customerCycleCompareMapper.selectCustomerBasicData(query);
            List<CustomerCycleCompareSummaryResp> idleDataList = customerCycleCompareMapper.selectCustomerIdleData(query);

            // 获取总消耗数据
            List<CustomerCycleCompareSummaryResp> spentDataList = customerCycleCompareMapper.selectCustomerSpentData(query);
            Map<Long, BigDecimal> spentDataMap = spentDataList.stream()
                    .collect(Collectors.toMap(
                            CustomerCycleCompareSummaryResp::getCustomerId,
                            CustomerCycleCompareSummaryResp::getTotalSpent
                    ));

            // 获取智能近7天平均单天消耗数据（基于每个客户最后一笔消耗时间倒推7天）
            List<CustomerCycleCompareSummaryResp> smartRecentSpentList = customerCycleCompareMapper.selectCustomerSmartRecentSpentData(query.getCustomerIds());

            Map<Long, BigDecimal> smartRecentSpentMap = smartRecentSpentList.stream()
                    .collect(Collectors.toMap(
                            CustomerCycleCompareSummaryResp::getCustomerId,
                            CustomerCycleCompareSummaryResp::getAverageDaySpent
                    ));

            // 将数据按客户ID分组
            Map<Long, CustomerCycleCompareSummaryResp> basicDataMap = basicDataList.stream()
                    .collect(Collectors.toMap(CustomerCycleCompareSummaryResp::getCustomerId, Function.identity()));

            Map<Long, CustomerCycleCompareSummaryResp> idleDataMap = idleDataList.stream()
                    .collect(Collectors.toMap(CustomerCycleCompareSummaryResp::getCustomerId, Function.identity()));

            // 合并数据
            List<CustomerCycleCompareSummaryResp> result = new ArrayList<>();

            for (Long customerId : query.getCustomerIds()) {
                CustomerCycleCompareSummaryResp summaryResp = new CustomerCycleCompareSummaryResp();
                summaryResp.setCustomerId(customerId);

                // 设置基础数据
                CustomerCycleCompareSummaryResp basicData = basicDataMap.get(customerId);
                if (basicData != null) {
                    summaryResp.setTotalAccounts(basicData.getTotalAccounts());
                    summaryResp.setTotalNormalAccounts(basicData.getTotalNormalAccounts());
                    summaryResp.setTotalDeadAccounts(basicData.getTotalDeadAccounts());
                    summaryResp.setTotalRecycledAccounts(basicData.getTotalRecycledAccounts());
                    summaryResp.setCreateTime(basicData.getCreateTime());
                } else {
                    summaryResp.setTotalAccounts(0);
                    summaryResp.setTotalNormalAccounts(0);
                    summaryResp.setTotalDeadAccounts(0);
                    summaryResp.setTotalRecycledAccounts(0);
                }

                // 设置空置户数据
                CustomerCycleCompareSummaryResp idleData = idleDataMap.get(customerId);
                if (idleData != null) {
                    summaryResp.setTotalIdleAccounts(idleData.getTotalIdleAccounts());
                } else {
                    summaryResp.setTotalIdleAccounts(0);
                }

                // 设置消耗数据
                BigDecimal totalSpent = spentDataMap.get(customerId);
                if (totalSpent != null) {
                    summaryResp.setTotalSpent(totalSpent);
                } else {
                    summaryResp.setTotalSpent(BigDecimal.ZERO);
                }

                // 计算平均户消耗
                if (summaryResp.getTotalAccounts() != null && summaryResp.getTotalAccounts() > 0 && summaryResp.getTotalSpent() != null) {
                    BigDecimal averageSpentPerAccount = summaryResp.getTotalSpent()
                            .divide(BigDecimal.valueOf(summaryResp.getTotalAccounts()), 2, RoundingMode.HALF_UP);
                    summaryResp.setAverageSpentPerAccount(averageSpentPerAccount);
                } else {
                    summaryResp.setAverageSpentPerAccount(BigDecimal.ZERO);
                }

                // 设置智能计算的平均单天消耗（基于客户最后消耗时间倒推7天的实际平均值）
                BigDecimal smartAverageDaySpent = smartRecentSpentMap.get(customerId);
                if (smartAverageDaySpent != null) {
                    summaryResp.setAverageDaySpent(smartAverageDaySpent);
                } else {
                    summaryResp.setAverageDaySpent(BigDecimal.ZERO);
                }

                result.add(summaryResp);
            }

            return result;
        } catch (Exception e) {
            log.error("查询客户周期对比汇总数据异常", e);
            return Collections.emptyList();
        }
    }


    /**
     * 获取客户周期统计数据（批量查询优化版本）
     *
     * @param query 查询条件
     * @return 客户周期统计数据，key是客户ID，value是客户对应的周期统计数据
     */
    @Override
    public Map<String, List<CustomerCycleComparePeroidResp>> listCustomerPeriodData(CustomerCycleComparePeriodQuery query) {
        Map<String, List<CustomerCycleComparePeroidResp>> resultMap = new HashMap<>();
        
        // 生成周期列表
        List<String> periods = generatePeriods(query.getPeriodType(), query.getComparePeriods());
        
        // 批量查询所有周期的数据
        Map<String, Map<Long, CustomerCycleCompareBatchResp>> batchBasicDataMap = batchQueryBasicData(query.getCustomerIds(), periods, query.getPeriodType());
        Map<String, Map<Long, CustomerCycleCompareBatchResp>> batchSpentDataMap = batchQuerySpentData(query.getCustomerIds(), periods, query.getPeriodType());
        Map<String, Map<String, Map<Long, Integer>>> batchRangeDistributionMap = batchQueryRangeDistribution(query.getCustomerIds(), periods, query.getPeriodType(), query.getSpentRanges());
        
        // 为每个客户组装周期数据
        for (Long customerId : query.getCustomerIds()) {
            List<CustomerCycleComparePeroidResp> customerPeriodData = new ArrayList<>();
            
            for (String period : periods) {
                CustomerCycleComparePeroidResp periodResp = new CustomerCycleComparePeroidResp();
                periodResp.setPeriod(period);
                
                // 从批量查询结果中获取基础数据
                CustomerCycleCompareBatchResp basicData = batchBasicDataMap.getOrDefault(period, new HashMap<>()).get(customerId);
                if (basicData != null) {
                    periodResp.setTotalAccounts(basicData.getTotalAccounts());
                    periodResp.setNormalAccounts(basicData.getTotalNormalAccounts());
                    periodResp.setDeadAccounts(basicData.getTotalDeadAccounts());
                    periodResp.setRecycledAccounts(basicData.getTotalRecycledAccounts());
                }
                
                // 从批量查询结果中获取消耗数据
                CustomerCycleCompareBatchResp spentData = batchSpentDataMap.getOrDefault(period, new HashMap<>()).get(customerId);
                if (spentData != null) {
                    periodResp.setTotalSpent(spentData.getTotalSpent());
                } else {
                    periodResp.setTotalSpent(BigDecimal.ZERO);
                }
                
                // 从批量查询结果中获取消耗区间分布
                Map<String, Map<Long, Integer>> periodRangeMap = batchRangeDistributionMap.getOrDefault(period, new HashMap<>());
                Map<String, Integer> customerRangeDistribution = new HashMap<>();
                for (Map.Entry<String, Map<Long, Integer>> rangeEntry : periodRangeMap.entrySet()) {
                    Integer count = rangeEntry.getValue().get(customerId);
                    if (count != null && count > 0) {
                        customerRangeDistribution.put(rangeEntry.getKey(), count);
                    }
                }
                periodResp.setSpentRangeDistribution(customerRangeDistribution);
                
                customerPeriodData.add(periodResp);
            }
            
            resultMap.put(customerId.toString(), customerPeriodData);
        }
        
        return resultMap;
    }
    

    
    /**
     * 生成周期列表
     *
     * @param periodType 周期类型（1=天，2=周，3=月）
     * @param comparePeriods 对比周期数
     * @return 周期标识列表
     */
    private List<String> generatePeriods(Integer periodType, Integer comparePeriods) {
        List<String> periods = new ArrayList<>();
        LocalDate now = LocalDate.now();
        
        for (int i = 0; i < comparePeriods; i++) {
            String period;
            switch (periodType) {
                case 1: // 天
                    period = now.minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    break;
                case 2: // 周
                    LocalDate weekStart = now.minusWeeks(i).with(DayOfWeek.MONDAY);
                    period = formatWeekPeriod(weekStart);
                    break;
                case 3: // 月
                    period = now.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    break;
                default:
                    throw new IllegalArgumentException("不支持的周期类型: " + periodType);
            }
            periods.add(period);
        }
        
        return periods;
    }
    
    /**
     * 格式化周期为易理解的格式
     *
     * @param weekStart 周开始日期
     * @return 格式化后的周期字符串，如"6月-第一周"
     */
    private String formatWeekPeriod(LocalDate weekStart) {
        int month = weekStart.getMonthValue();
        int weekOfMonth = getWeekOfMonth(weekStart);
        
        String weekText;
        switch (weekOfMonth) {
            case 1:
                weekText = "第一周";
                break;
            case 2:
                weekText = "第二周";
                break;
            case 3:
                weekText = "第三周";
                break;
            case 4:
                weekText = "第四周";
                break;
            case 5:
                weekText = "第五周";
                break;
            default:
                weekText = "第" + weekOfMonth + "周";
                break;
        }
        
        return month + "月-" + weekText;
    }
    
    /**
     * 计算某个日期是当月的第几周
     *
     * @param date 日期
     * @return 当月的第几周
     */
    private int getWeekOfMonth(LocalDate date) {
        LocalDate firstDayOfMonth = date.withDayOfMonth(1);
        LocalDate firstMondayOfMonth = firstDayOfMonth.with(DayOfWeek.MONDAY);
        
        // 如果第一个周一在上个月，则从本月第一天开始算第一周
        if (firstMondayOfMonth.isBefore(firstDayOfMonth)) {
            firstMondayOfMonth = firstMondayOfMonth.plusWeeks(1);
        }
        
        long weeksBetween = ChronoUnit.WEEKS.between(firstMondayOfMonth, date);
        return (int) weeksBetween + 1;
    }
    
    /**
     * 计算周期的时间范围
     *
     * @param period 周期标识
     * @param periodType 周期类型
     * @return 时间范围数组 [开始时间, 结束时间]
     */
    private LocalDateTime[] calculatePeriodRange(String period, Integer periodType) {
        LocalDateTime startDateTime, endDateTime;

        switch (periodType) {
            case 1: // 天
                LocalDate date = LocalDate.parse(period);
                startDateTime = date.atStartOfDay(); // 00:00:00
                endDateTime = date.atTime(23, 59, 59); // 23:59:59
                break;
            case 2: // 周
                LocalDate[] weekRange = parseWeekPeriod(period);
                startDateTime = weekRange[0].atStartOfDay(); // 周一开始 00:00:00
                endDateTime = weekRange[1].atTime(23, 59, 59); // 周日结束 23:59:59
                break;
            case 3: // 月
                YearMonth yearMonth = YearMonth.parse(period);
                startDateTime = yearMonth.atDay(1).atStartOfDay(); // 月初 00:00:00
                endDateTime = yearMonth.atEndOfMonth().atTime(23, 59, 59); // 月末 23:59:59
                break;
            default:
                throw new IllegalArgumentException("不支持的周期类型: " + periodType);
        }

        return new LocalDateTime[]{startDateTime, endDateTime};
    }
    
    /**
     * 解析周期格式"6月-第一周"为日期范围
     *
     * @param period 周期字符串，格式如"6月-第一周"
     * @return 日期范围数组 [开始日期, 结束日期]
     */
    private LocalDate[] parseWeekPeriod(String period) {
        try {
            // 解析格式："6月-第一周"
            String[] parts = period.split("-");
            if (parts.length != 2) {
                throw new IllegalArgumentException("周期格式错误: " + period);
            }
            
            // 解析月份
            String monthStr = parts[0].replace("月", "");
            int month = Integer.parseInt(monthStr);
            
            // 解析周数
            String weekStr = parts[1];
            int weekOfMonth;
            if (weekStr.equals("第一周")) {
                weekOfMonth = 1;
            } else if (weekStr.equals("第二周")) {
                weekOfMonth = 2;
            } else if (weekStr.equals("第三周")) {
                weekOfMonth = 3;
            } else if (weekStr.equals("第四周")) {
                weekOfMonth = 4;
            } else if (weekStr.equals("第五周")) {
                weekOfMonth = 5;
            } else {
                // 处理"第X周"格式
                String numStr = weekStr.replace("第", "").replace("周", "");
                weekOfMonth = Integer.parseInt(numStr);
            }
            
            // 计算当前年份（可以根据实际需求调整）
            int currentYear = LocalDate.now().getYear();
            
            // 计算该月第一天
            LocalDate firstDayOfMonth = LocalDate.of(currentYear, month, 1);
            
            // 找到该月第一个周一
            LocalDate firstMondayOfMonth = firstDayOfMonth.with(DayOfWeek.MONDAY);
            if (firstMondayOfMonth.isBefore(firstDayOfMonth)) {
                firstMondayOfMonth = firstMondayOfMonth.plusWeeks(1);
            }
            
            // 计算目标周的开始日期
            LocalDate startDate = firstMondayOfMonth.plusWeeks(weekOfMonth - 1);
            LocalDate endDate = startDate.plusDays(6);
            
            return new LocalDate[]{startDate, endDate};
        } catch (Exception e) {
            throw new IllegalArgumentException("解析周期失败: " + period, e);
        }
    }
    
    /**
     * 计算消耗区间分布（使用SQL直接统计）
     *
     * @param customerId 客户ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param spentRangeConfigs 消耗区间配置
     * @return 消耗区间分布
     */
    private Map<String, Integer> calculateSpentRangeDistribution(Long customerId, LocalDate startDate, LocalDate endDate, List<CustomerCycleComparePeriodQuery.SpentRangeConfig> spentRangeConfigs) {
        Map<String, Integer> distribution = new LinkedHashMap<>();
        
        // 如果没有配置，使用默认配置
        if (spentRangeConfigs == null || spentRangeConfigs.isEmpty()) {
            spentRangeConfigs = getDefaultSpentRangeConfigs();
        }
        
        // 初始化所有区间为0
        for (CustomerCycleComparePeriodQuery.SpentRangeConfig config : spentRangeConfigs) {
            distribution.put(config.getLabel(), 0);
        }
        
        // 使用SQL直接查询区间分布
        List<Map<String, Object>> rangeDistributionList = customerCycleCompareMapper.selectAccountSpentRangeDistribution(
            customerId, startDate, endDate, spentRangeConfigs);
        
        // 填充查询结果
        for (Map<String, Object> rangeData : rangeDistributionList) {
            String spendRange = (String) rangeData.get("spend_range");
            Long accountCount = (Long) rangeData.get("account_count");
            distribution.put(spendRange, accountCount.intValue());
        }
        
        return distribution;
    }
    
    /**
     * 获取默认的消耗区间配置
     *
     * @return 默认消耗区间配置列表
     */
    private List<CustomerCycleComparePeriodQuery.SpentRangeConfig> getDefaultSpentRangeConfigs() {
        List<CustomerCycleComparePeriodQuery.SpentRangeConfig> configs = new ArrayList<>();
        
        CustomerCycleComparePeriodQuery.SpentRangeConfig config1 = new CustomerCycleComparePeriodQuery.SpentRangeConfig();
        config1.setLabel("0-500");
        config1.setMin(0);
        config1.setMax(500);
        configs.add(config1);
        
        CustomerCycleComparePeriodQuery.SpentRangeConfig config2 = new CustomerCycleComparePeriodQuery.SpentRangeConfig();
        config2.setLabel("500-1k");
        config2.setMin(500);
        config2.setMax(1000);
        configs.add(config2);
        
        CustomerCycleComparePeriodQuery.SpentRangeConfig config3 = new CustomerCycleComparePeriodQuery.SpentRangeConfig();
        config3.setLabel("1k-5k");
        config3.setMin(1000);
        config3.setMax(5000);
        configs.add(config3);
        
        CustomerCycleComparePeriodQuery.SpentRangeConfig config4 = new CustomerCycleComparePeriodQuery.SpentRangeConfig();
        config4.setLabel("5k-1w");
        config4.setMin(5000);
        config4.setMax(10000);
        configs.add(config4);
        
        CustomerCycleComparePeriodQuery.SpentRangeConfig config5 = new CustomerCycleComparePeriodQuery.SpentRangeConfig();
        config5.setLabel("1w+");
        config5.setMin(10000);
        config5.setMax(null);
        configs.add(config5);
        
        return configs;
    }

    /**
     * 批量查询基础数据
     *
     * @param customerIds 客户ID列表
     * @param periods 周期列表
     * @param periodType 周期类型
     * @return 批量基础数据，key是周期，value是客户ID到数据的映射
     */
    private Map<String, Map<Long, CustomerCycleCompareBatchResp>> batchQueryBasicData(List<Long> customerIds, List<String> periods, Integer periodType) {
        Map<String, Map<Long, CustomerCycleCompareBatchResp>> resultMap = new HashMap<>();
        
        for (String period : periods) {
            LocalDateTime[] periodRange = calculatePeriodRange(period, periodType);
            LocalDateTime startDate = periodRange[0];
            LocalDateTime endDate = periodRange[1];
            
            List<CustomerCycleCompareBatchResp> batchData = customerCycleCompareMapper.selectBatchCustomerBasicData(
                customerIds, period, startDate, endDate);
            
            Map<Long, CustomerCycleCompareBatchResp> periodDataMap = batchData.stream()
                .collect(Collectors.toMap(CustomerCycleCompareBatchResp::getCustomerId, Function.identity()));
            
            resultMap.put(period, periodDataMap);
        }
        
        return resultMap;
    }

    /**
     * 批量查询消耗数据
     *
     * @param customerIds 客户ID列表
     * @param periods 周期列表
     * @param periodType 周期类型
     * @return 批量消耗数据，key是周期，value是客户ID到数据的映射
     */
    private Map<String, Map<Long, CustomerCycleCompareBatchResp>> batchQuerySpentData(List<Long> customerIds, List<String> periods, Integer periodType) {
        Map<String, Map<Long, CustomerCycleCompareBatchResp>> resultMap = new HashMap<>();
        
        for (String period : periods) {
            LocalDateTime[] periodRange = calculatePeriodRange(period, periodType);
            LocalDateTime startDate = periodRange[0];
            LocalDateTime endDate = periodRange[1];
            
            List<CustomerCycleCompareBatchResp> batchData = customerCycleCompareMapper.selectBatchCustomerSpentData(
                customerIds, period, startDate, endDate);
            
            Map<Long, CustomerCycleCompareBatchResp> periodDataMap = batchData.stream()
                .collect(Collectors.toMap(CustomerCycleCompareBatchResp::getCustomerId, Function.identity()));
            
            resultMap.put(period, periodDataMap);
        }
        
        return resultMap;
    }

    /**
     * 批量查询消耗区间分布
     *
     * @param customerIds 客户ID列表
     * @param periods 周期列表
     * @param periodType 周期类型
     * @param spentRanges 消耗区间配置
     * @return 批量区间分布数据，key是周期，value是区间到客户ID计数的映射
     */
    private Map<String, Map<String, Map<Long, Integer>>> batchQueryRangeDistribution(List<Long> customerIds, List<String> periods, Integer periodType, List<CustomerCycleComparePeriodQuery.SpentRangeConfig> spentRanges) {
        Map<String, Map<String, Map<Long, Integer>>> resultMap = new HashMap<>();
        
        // 如果没有配置消耗区间，使用默认配置
        if (CollUtil.isEmpty(spentRanges)) {
            spentRanges = getDefaultSpentRangeConfigs();
        }
        
        for (String period : periods) {
            LocalDateTime[] periodRange = calculatePeriodRange(period, periodType);
            LocalDateTime startDate = periodRange[0];
            LocalDateTime endDate = periodRange[1];
            
            List<Map<String, Object>> batchData = customerCycleCompareMapper.selectBatchAccountSpentRangeDistribution(
                customerIds, period, startDate, endDate, spentRanges);
            
            Map<String, Map<Long, Integer>> periodRangeMap = new HashMap<>();
            
            for (Map<String, Object> data : batchData) {
                Long customerId = ((Number) data.get("customerId")).longValue();
                String spendRange = (String) data.get("spend_range");
                Integer accountCount = ((Number) data.get("account_count")).intValue();
                
                periodRangeMap.computeIfAbsent(spendRange, k -> new HashMap<>()).put(customerId, accountCount);
            }
            
            resultMap.put(period, periodRangeMap);
        }
        
        return resultMap;
    }

}