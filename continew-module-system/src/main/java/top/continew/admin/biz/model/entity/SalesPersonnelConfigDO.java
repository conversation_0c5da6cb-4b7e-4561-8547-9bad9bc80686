package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 商务人员配置数据实体
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
@Data
@TableName("biz_sales_personnel_config")
public class SalesPersonnelConfigDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    private Long businessUserId;

    /**
     * 统计月份
     */
    private LocalDate salesDate;

    /**
     * 配置数据
     */
    private String data;
}