package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.resp.crm.CrmWorkbenchResp;

/**
 * CRM公共服务
 *
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/3 14:52
 */
public interface CrmCommonService {


    
    /**
     * 获取CRM工作台统计数据
     *
     * @param showLeadCount 是否显示线索统计
     * @param showOpportunityCount 是否显示商机统计
     * @return CRM工作台统计数据
     */
    CrmWorkbenchResp getCrmWorkbenchStats(boolean showLeadCount, boolean showOpportunityCount, boolean showVisitTaskCount);
}
