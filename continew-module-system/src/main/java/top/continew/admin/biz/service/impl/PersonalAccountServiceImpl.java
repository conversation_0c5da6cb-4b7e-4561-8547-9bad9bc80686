package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.admin.biz.mapper.PersonalAccountMapper;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.query.PersonalAccountQuery;
import top.continew.admin.biz.model.req.PersonAccountBatchUpdateReq;
import top.continew.admin.biz.model.req.PersonalAccountReq;
import top.continew.admin.biz.model.resp.PersonalAccountDetailResp;
import top.continew.admin.biz.model.resp.PersonalAccountResp;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.admin.biz.service.PersonalAccountService;
import top.continew.admin.biz.service.WhiteEmailService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 个号业务实现
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalAccountServiceImpl extends BaseServiceImpl<PersonalAccountMapper, PersonalAccountDO, PersonalAccountResp, PersonalAccountDetailResp, PersonalAccountQuery, PersonalAccountReq> implements PersonalAccountService {

    private final WhiteEmailService whiteEmailService;

    private final BusinessManagerService businessManagerService;

    @Override
    protected QueryWrapper<PersonalAccountDO> buildQueryWrapper(PersonalAccountQuery query) {
        QueryWrapper<PersonalAccountDO> wrapper = super.buildQueryWrapper(query);
        if (StringUtils.isNotBlank(query.getRemark())) {
            if ("空".equals(query.getRemark())) {
                wrapper.eq("remark", StrUtil.EMPTY);
            } else {
                wrapper.like("remark", query.getRemark());
            }
        }
        return wrapper;
    }

    @Override
    public List<LabelValueResp<String>> getChannelList() {
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PersonalAccountReq req, Long id) {
        this.beforeUpdate(req, id);
        PersonalAccountDO entity = this.getById(id);
        if (!req.getBrowserId().equals(entity.getBrowserId()) && !entity.getBrowserNo().equals(req.getBrowserNo())) {
            // 更新bm的个号浏览器信息
            List<String> browserNos = new ArrayList<>();
            if (StringUtils.isNotBlank(entity.getBrowserNo())) {
                browserNos.add(entity.getBrowserNo());
            }
            if (StringUtils.isNotBlank(entity.getBrowserId())) {
                browserNos.add(entity.getBrowserId());
            }
            businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getOpsBrowser, req.getBrowserNo())
                .in(BusinessManagerDO::getOpsBrowser, browserNos));
            businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getReserveBrowser, req.getBrowserNo())
                .in(BusinessManagerDO::getReserveBrowser, browserNos));
            businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getReserveBrowserBak, req.getBrowserNo())
                .in(BusinessManagerDO::getReserveBrowserBak, browserNos));
            businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getObserveBrowser, req.getBrowserNo())
                .in(BusinessManagerDO::getObserveBrowser, browserNos));
        }
        BeanUtil.copyProperties(req, entity, CopyOptions.create().ignoreNullValue());
        this.baseMapper.updateById(entity);
        this.afterUpdate(req, entity);
    }

    @Override
    public void export(PersonalAccountQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<PersonalAccountResp> list = baseMapper.selectCustomList(this.buildQueryWrapper(query));
        list.forEach(this::fill);
        List<PersonalAccountDetailResp> result = BeanUtil.copyToList(list, PersonalAccountDetailResp.class);
        ExcelUtils.export(result, "导出数据", this.getDetailClass(), response);
    }

    @Override
    protected void beforeAdd(PersonalAccountReq req) {
        Set<String> platformAccountIdSet = getPlatformAccountIdSet();
        String platformAccountId = req.getContent().split("\\|")[0];
        if (platformAccountIdSet.contains(platformAccountId)) {
            throw new BusinessException("广告户ID已存在");
        } else {
            req.setPlatformAccountId(platformAccountId);
            if (req.getContent().split("\\|").length > 1) {
                req.setEmail(req.getContent().split("\\|")[1].trim());
            }
        }
        req.setUniqueKey(RandomUtil.randomString(8));
    }

    @Override
    protected void afterAdd(PersonalAccountReq req, PersonalAccountDO entity) {
        List<String> emails = CommonUtils.extractEmails(req.getContent());
        whiteEmailService.saveByEmail(emails);
    }

    @Override
    public void importExcel(MultipartFile file,
                            Long channelId,
                            BigDecimal unitPrice,
                            LocalDate purchaseTime,
                            Boolean isAfterSale,
                            PersonalAccoutTypeEnum type) {
        CheckUtils.throwIfNull(channelId, "渠道不能为空");
        CheckUtils.throwIfNull(unitPrice, "单价不能为空");
        CheckUtils.throwIfNull(purchaseTime, "购买时间不能为空");
        CheckUtils.throwIfNull(isAfterSale, "售后补号不能为空");
        CheckUtils.throwIfNull(file, "文件不能为空");
        CheckUtils.throwIfNull(type, "类型不能为空");
        List<String> txtData = new ArrayList<>();
        try {
            IoUtil.readUtf8Lines(file.getInputStream(), txtData);
        } catch (IOException e) {
            log.error("个号导入数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        List<PersonalAccountDO> saveList = new ArrayList<>();
        List<String> emails = new ArrayList<>();
        Set<String> platformAccountIdSet = getPlatformAccountIdSet();
        for (String txtDatum : txtData) {
            if (StringUtils.isBlank(txtDatum)) {
                continue;
            }
            String[] split = txtDatum.split("\t");
            String platformAccountId = split[0].split("\\|")[0];
            String email = split[0].split("\\|")[1].trim();
            if (platformAccountIdSet.contains(platformAccountId)) {
                continue;
            }
            platformAccountIdSet.add(platformAccountId);
            PersonalAccountDO personalAccountDO = new PersonalAccountDO();
            personalAccountDO.setUnitPrice(unitPrice);
            personalAccountDO.setChannelId(channelId);
            personalAccountDO.setPurchaseTime(purchaseTime.atStartOfDay());
            personalAccountDO.setContent(split[0]);
            personalAccountDO.setType(type);
            if (split.length > 1) {
                personalAccountDO.setBrowserNo(split[1]);
            }
            personalAccountDO.setUniqueKey(RandomUtil.randomString(8));
            personalAccountDO.setPlatformAccountId(platformAccountId);
            personalAccountDO.setEmail(email);
            personalAccountDO.setIsAfterSale(isAfterSale);
            saveList.add(personalAccountDO);
            emails.addAll(CommonUtils.extractEmails(split[0]));
        }
        saveBatch(saveList);
        whiteEmailService.saveByEmail(emails);
    }

    @Override
    public PageResp<PersonalAccountResp> page(PersonalAccountQuery query, PageQuery pageQuery) {
        QueryWrapper<PersonalAccountDO> wrapper = this.buildQueryWrapper(query);
        wrapper.orderByDesc("pa.id");
        IPage<PersonalAccountResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), wrapper);
        PageResp<PersonalAccountResp> pageResp = PageResp.build(page, this.getListClass());
        pageResp.getList().forEach(this::fill);
        return pageResp;
    }

    @Override
    public void access(Long id) {
        this.update(Wrappers.<PersonalAccountDO>lambdaUpdate()
            .set(PersonalAccountDO::getIsAccess, true)
            .set(PersonalAccountDO::getAccessUser, UserContextHolder.getNickname())
            .eq(PersonalAccountDO::getId, id));
    }

    @Override
    public void batchUpdate(PersonAccountBatchUpdateReq req) {
        boolean hasUpdateFields = StringUtils.isNotBlank(req.getRemark()) || StringUtils.isNotBlank(req.getAfterSaleReason()) || req.getAppealStatus() != null || req.getAfterSaleStatus() != null || req.getIsChangePwd() != null || req.getType() != null || req.getIsAfterSale() != null || req.getAccountStatus() != null;
        if (!hasUpdateFields) {
            return;
        }
        this.update(Wrappers.<PersonalAccountDO>lambdaUpdate()
            .set(StringUtils.isNotBlank(req.getAfterSaleReason()), PersonalAccountDO::getAfterSaleReason, req.getAfterSaleReason())
            .set(req.getAppealStatus() != null, PersonalAccountDO::getAppealStatus, req.getAppealStatus())
            .set(req.getAfterSaleStatus() != null, PersonalAccountDO::getAfterSaleStatus, req.getAfterSaleStatus())
            .set(req.getIsChangePwd() != null, PersonalAccountDO::getIsChangePwd, req.getIsChangePwd())
            .set(req.getType() != null, PersonalAccountDO::getType, req.getType())
            .set(req.getIsAfterSale() != null, PersonalAccountDO::getIsAfterSale, req.getIsAfterSale())
            .set(req.getRemark() != null, PersonalAccountDO::getRemark, req.getRemark())
            .set(req.getAccountStatus() != null, PersonalAccountDO::getAccountStatus, req.getAccountStatus())
            .in(PersonalAccountDO::getId, req.getIds()));
    }

    private Set<String> getPlatformAccountIdSet() {
        return list().stream().map(PersonalAccountDO::getPlatformAccountId).collect(Collectors.toSet());
    }
}