/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.AppealOrderCancelEvent;
import top.continew.admin.biz.event.AppealOrderCreateEvent;
import top.continew.admin.biz.event.AppealOrderSuccessEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AppealOrderDO;
import top.continew.admin.biz.model.resp.AdAccountCardClearResultResp;
import top.continew.admin.biz.model.resp.AdAccountCardOpsResultResp;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.AppealOrderService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class AppealOrderListener {

    private final AdAccountService adAccountService;

    private final AppealOrderService appealOrderService;

    @EventListener
    public void create(AppealOrderCreateEvent event) {
        Long orderId = (Long)event.getSource();
        AppealOrderDO order = appealOrderService.getById(orderId);
        // 更新广告户申诉状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getAppealStatus, AdAccountAppealStatusEnum.PROCESS)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        List<AdAccountCardClearResultResp> clearResultRespList = new ArrayList<>();
        List<AdAccountCardOpsResultResp> resultRespList = adAccountService.withdrawAllCards(order.getPlatformAdId());
        adAccountService.updateCardStatus(order.getPlatformAdId(), false);
        BigDecimal totalWithdrawalAmount = BigDecimal.ZERO;
        int successCount = 0;
        for (AdAccountCardOpsResultResp adAccountCardOpsResultResp : resultRespList) {
            if (adAccountCardOpsResultResp.getIsSuccess()) {
                successCount++;
                totalWithdrawalAmount = totalWithdrawalAmount.add(adAccountCardOpsResultResp.getAmount());
            }
            AdAccountCardClearResultResp item = AdAccountCardClearResultResp.builder()
                .cardNumber(adAccountCardOpsResultResp.getCardNumber())
                .platform(Optional.ofNullable(adAccountCardOpsResultResp.getPlatform())
                    .map(CardPlatformEnum::getValue)
                    .orElse(null))
                .isCancel(false)
                .isSuccess(adAccountCardOpsResultResp.getIsSuccess())
                .message(adAccountCardOpsResultResp.getMessage())
                .withdrawalAmount(adAccountCardOpsResultResp.getAmount())
                .build();
            clearResultRespList.add(item);
        }
        AppealOrderCardStatusEnum statusEnum;
        if (successCount == 0) {
            statusEnum = AppealOrderCardStatusEnum.FAIL;
        } else if (successCount == resultRespList.size()) {
            statusEnum = AppealOrderCardStatusEnum.SUCCESS;
        } else {
            statusEnum = AppealOrderCardStatusEnum.PART_SUCCESS;
        }
        appealOrderService.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getCardClearResult, JSON.toJSONString(clearResultRespList))
            .set(AppealOrderDO::getCardStatus, statusEnum)
            .set(AppealOrderDO::getCardBalance, totalWithdrawalAmount)
            .eq(AppealOrderDO::getId, orderId));
    }

    @Async
    @EventListener
    public void success(AppealOrderSuccessEvent event) {
        Long orderId = (Long)event.getSource();
        AppealOrderDO order = appealOrderService.getById(orderId);
        log.info("【广告户申诉】订单{}申诉成功", order.getOrderNo());
        if (order.getCardBalance().compareTo(BigDecimal.ZERO) > 0) {
            AdAccountDO adAccount = adAccountService.getByPlatformAdId(order.getPlatformAdId());
            if (adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT) && adAccount.getSaleStatus()
                .equals(AdAccountSaleStatusEnum.SALT)) {
                adAccountService.updateCardStatus(order.getPlatformAdId(), true);
                AdAccountCardOpsResultResp resultResp = adAccountService.rechargeMasterCard(order.getPlatformAdId(), order.getCardBalance(), false);
                if (resultResp.getIsSuccess()) {
                    appealOrderService.update(Wrappers.<AppealOrderDO>lambdaUpdate()
                        .set(AppealOrderDO::getCardStatus, AppealOrderCardStatusEnum.REVOKE)
                        .eq(AppealOrderDO::getId, orderId));
                }
            }
        }
    }

    @Async
    @EventListener
    public void cancel(AppealOrderCancelEvent event) {
        Long orderId = (Long)event.getSource();
        AppealOrderDO order = appealOrderService.getById(orderId);
        log.info("【广告户申诉】订单{}申诉取消", order.getOrderNo());
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getAppealStatus, AdAccountAppealStatusEnum.WAIT)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        if (order.getCardBalance().compareTo(BigDecimal.ZERO) > 0) {
            AdAccountDO adAccount = adAccountService.getByPlatformAdId(order.getPlatformAdId());
            if (adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT) && adAccount.getSaleStatus()
                .equals(AdAccountSaleStatusEnum.SALT)) {
                adAccountService.updateCardStatus(order.getPlatformAdId(), true);
                AdAccountCardOpsResultResp resultResp = adAccountService.rechargeMasterCard(order.getPlatformAdId(), order.getCardBalance(), false);
                if (resultResp.getIsSuccess()) {
                    appealOrderService.update(Wrappers.<AppealOrderDO>lambdaUpdate()
                        .set(AppealOrderDO::getCardStatus, AppealOrderCardStatusEnum.REVOKE)
                        .eq(AppealOrderDO::getId, orderId));
                }
            }
        }
        //        if (order.getApplyMessageId() != null && order.getCustomerId() != null) {
        //            CustomerDO customer = customerService.getById(order.getCustomerId());
        //            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //                .chatId(customer.getTelegramChatId())
        //                .replyToMessageId(order.getApplyMessageId())
        //                .text(order.getPlatformAdId() + " 取消申诉")
        //                .build()));
        //        }
    }
}
