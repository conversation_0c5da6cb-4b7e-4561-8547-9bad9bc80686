package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务人员配置数据参数
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
@Data
@Schema(description = "创建或修改商务人员配置数据参数")
public class SalesPersonnelConfigReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @NotNull(message = "关联商务不能为空")
    private Long businessUserId;

    /**
     * 统计月份
     */
    @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime startDate;

    @Schema(description = "结束时间", example = "2025-01-31 23:59:59")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime endDate;
    /**
     * 配置数据
     */
    @Schema(description = "配置数据")
    @NotBlank(message = "配置数据不能为空")
    @Length(max = 65535, message = "配置数据长度不能超过 {max} 个字符")
    private String data;
}