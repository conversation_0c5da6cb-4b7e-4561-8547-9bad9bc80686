package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 商务人员配置数据信息
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
@Data
@Schema(description = "商务人员配置数据信息")
public class SalesPersonnelConfigResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    private Long businessUserId;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    private LocalDate salesDate;

    /**
     * 配置数据
     */
    @Schema(description = "配置数据")
    private String data;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
}