package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.SalesPersonnelConfigMapper;
import top.continew.admin.biz.model.entity.SalesPersonnelConfigDO;
import top.continew.admin.biz.model.query.SalesPersonnelConfigQuery;
import top.continew.admin.biz.model.req.SalesPersonnelConfigReq;
import top.continew.admin.biz.model.resp.SalesPersonnelConfigDetailResp;
import top.continew.admin.biz.model.resp.SalesPersonnelConfigResp;
import top.continew.admin.biz.service.SalesPersonnelConfigService;

/**
 * 商务人员配置数据业务实现
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
@Service
@RequiredArgsConstructor
public class SalesPersonnelConfigServiceImpl extends BaseServiceImpl<SalesPersonnelConfigMapper, SalesPersonnelConfigDO, SalesPersonnelConfigResp, SalesPersonnelConfigDetailResp, SalesPersonnelConfigQuery, SalesPersonnelConfigReq> implements SalesPersonnelConfigService {
    @Override
    public String getNewConfig(Long businessUserId) {
        SalesPersonnelConfigDO config = lambdaQuery()
                .eq(SalesPersonnelConfigDO::getBusinessUserId, businessUserId)
                .orderByDesc(SalesPersonnelConfigDO::getSalesDate)
                .last("limit 1")
                .one();
        return config != null ? config.getData() : null;
    }
}