package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.SalesPersonnelConfigDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SalesPersonnelConfigQuery;
import top.continew.admin.biz.model.req.SalesPersonnelConfigReq;
import top.continew.admin.biz.model.resp.SalesPersonnelConfigDetailResp;
import top.continew.admin.biz.model.resp.SalesPersonnelConfigResp;

/**
 * 商务人员配置数据业务接口
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
public interface SalesPersonnelConfigService extends BaseService<SalesPersonnelConfigResp, SalesPersonnelConfigDetailResp, SalesPersonnelConfigQuery, SalesPersonnelConfigReq>, IService<SalesPersonnelConfigDO> {

    String getNewConfig(Long businessUserId);

}