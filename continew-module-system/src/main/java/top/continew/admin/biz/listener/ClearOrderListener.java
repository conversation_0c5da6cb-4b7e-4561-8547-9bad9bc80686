/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.*;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.resp.AdAccountCardClearResultResp;
import top.continew.admin.biz.model.resp.AdAccountCardOpsResultResp;
import top.continew.admin.biz.service.*;
import top.continew.admin.system.service.FileService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class ClearOrderListener {

    private final CustomerService customerService;

    private final AdAccountService adAccountService;

    private final ClearOrderService clearOrderService;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final FileService fileService;

    private final AdAccountOrderService adAccountOrderService;

    /**
     * 提现卡台金额
     *
     * @param event
     */
    @Async
    @EventListener
    public void cardClear(ClearOrderCreateEvent event) {
        Long orderId = (Long)event.getSource();
        ClearOrderDO order = clearOrderService.getById(orderId);
        List<AdAccountCardClearResultResp> clearResultRespList = new ArrayList<>();
        List<AdAccountCardOpsResultResp> resultRespList = adAccountService.withdrawAllCards(order.getPlatformAdId());
        adAccountService.updateCardStatus(order.getPlatformAdId(), false);
        BigDecimal totalWithdrawalAmount = BigDecimal.ZERO;
        int successCount = 0;
        for (AdAccountCardOpsResultResp adAccountCardOpsResultResp : resultRespList) {
            if (adAccountCardOpsResultResp.getIsSuccess()) {
                successCount++;
                totalWithdrawalAmount = totalWithdrawalAmount.add(adAccountCardOpsResultResp.getAmount());
            }
            AdAccountCardClearResultResp item = AdAccountCardClearResultResp.builder()
                .cardNumber(adAccountCardOpsResultResp.getCardNumber())
                .platform(Optional.ofNullable(adAccountCardOpsResultResp.getPlatform())
                    .map(CardPlatformEnum::getValue)
                    .orElse(null))
                .isCancel(false)
                .isSuccess(adAccountCardOpsResultResp.getIsSuccess())
                .message(adAccountCardOpsResultResp.getMessage())
                .withdrawalAmount(adAccountCardOpsResultResp.getAmount())
                .build();
            clearResultRespList.add(item);
        }
        ClearOrderCardStatusEnum statusEnum;
        if (successCount == 0) {
            statusEnum = ClearOrderCardStatusEnum.FAIL;
        } else if (successCount == resultRespList.size()) {
            statusEnum = ClearOrderCardStatusEnum.SUCCESS;
        } else {
            statusEnum = ClearOrderCardStatusEnum.PART_SUCCESS;
        }
        clearOrderService.update(Wrappers.<ClearOrderDO>lambdaUpdate()
            .set(ClearOrderDO::getCardClearResult, JSON.toJSONString(clearResultRespList))
            .set(ClearOrderDO::getCardStatus, statusEnum)
            .set(ClearOrderDO::getCardBalance, totalWithdrawalAmount)
            .eq(ClearOrderDO::getId, orderId));
    }

    @EventListener(ClearOrderHandleEvent.class)
    public void handle(ClearOrderHandleEvent event) {
        //        Long orderId = (Long)event.getSource();
        //        ClearOrderDO order = clearOrderService.getById(orderId);
        //        String currentUser = UserContextHolder.getUsername();
        //        log.info("【清零订单】{}已被{}接单", orderId, currentUser);
        //        String message = order.getPlatformAdId() + " 正在清零中";
        //        CustomerDO customer = customerService.getById(order.getCustomerId());
        //        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //            .chatId(customer.getTelegramChatId())
        //            .replyToMessageId(order.getApplyMessageId())
        //            .text(message)
        //            .build()));
    }

    @EventListener(ClearOrderFinishEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void finish(ClearOrderFinishEvent event) {
        Long orderId = (Long)event.getSource();
        ClearOrderDO order = clearOrderService.getById(orderId);
        CustomerDO customer = customerService.getById(order.getCustomerId());
        log.info("【清零订单】{}已完成", orderId);
        customerService.changeAmount(order.getCustomerId(), order.getPlatformAdId(), order.getClearAmount(), CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR, null, null);
        log.info("【清零订单】{}客户余额添加成功", orderId);
        // 更新广告户清零状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.CLEARED)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        if (order.getAdAccountOrderId() != null) {
            adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getClearStatus, AdAccountClearStatusEnum.CLEARED)
                .set(AdAccountOrderDO::getClearTime, LocalDateTime.now())
                .eq(AdAccountOrderDO::getId, order.getAdAccountOrderId()));
        }
        log.info("【清零订单】{}广告户清零成功", orderId);
        adAccountService.updateCardStatus(order.getPlatformAdId(), false);
        String replyMsg = order.getPlatformAdId() + " 已清零 " + order.getClearAmount();
        InputFile inputFile = fileService.getFileInputFile(order.getCertificate());
        if (inputFile != null) {
            SendPhoto sendPhoto = SendPhoto.builder()
                .chatId(customer.getTelegramChatId())
                .photo(inputFile)
                .caption(replyMsg)
                .build();
            SpringUtil.publishEvent(new TelegramMessageEvent(sendPhoto));
        } else {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(customer.getTelegramChatId())
                .text(replyMsg)
                .build()));
        }
        // 发送额度汇总信息
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), order.getClearAmount());
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
    }

    @EventListener
    public void cancel(ClearOrderCancelEvent event) {
        Long orderId = (Long)event.getSource();
        ClearOrderDO order = clearOrderService.getById(orderId);
        log.info("【清零订单】{}已被取消", orderId);
        clearOrderService.update(Wrappers.<ClearOrderDO>lambdaUpdate()
            .set(ClearOrderDO::getStatus, ClearOrderStatusEnum.CANCEL)
            .eq(ClearOrderDO::getId, orderId));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        if (order.getAdAccountOrderId() != null) {
            adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getClearStatus, AdAccountClearStatusEnum.WAIT)
                .eq(AdAccountOrderDO::getId, order.getAdAccountOrderId()));
        }
        //        String message = order.getPlatformAdId() + " 取消清零";
        //        CustomerDO customer = customerService.getById(order.getCustomerId());
        //        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //            .chatId(customer.getTelegramChatId())
        //            .replyToMessageId(order.getApplyMessageId())
        //            .text(message)
        //            .build()));
        adAccountService.updateCardStatus(order.getPlatformAdId(), true);
        // 卡台金额回冲
        if (order.getCardBalance() != null && order.getCardBalance().compareTo(BigDecimal.ZERO) > 0) {
            AdAccountCardOpsResultResp resultResp = adAccountService.rechargeMasterCard(order.getPlatformAdId(), order.getCardBalance(), false);
            if (resultResp.getIsSuccess()) {
                clearOrderService.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                    .set(ClearOrderDO::getCardStatus, ClearOrderCardStatusEnum.REVOKE)
                    .eq(ClearOrderDO::getId, orderId));
            }
        }
    }
}
