/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.*;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.req.AdsPowerOpenReq;
import top.continew.admin.biz.report.model.BillingPaymentAccountModel;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.cache.redisson.util.RedisUtils;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class AdAccountListener {

    private final AdAccountService adAccountService;

    private final AdAccountCardService adAccountCardService;

    private final AdAccountOrderService adAccountOrderService;

    private final AppealOrderService appealOrderService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final BusinessManagerItemService businessManagerItemService;

    private final CardService cardService;

    private final CustomerService customerService;

    private final AdsPowerService adsPowerService;

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final AdAccountOrderMapper adAccountOrderMapper;

    @EventListener
    public void add(AdAccountAddEvent event) {
        AdAccountDO account = (AdAccountDO)event.getSource();
        boolean exist = adAccountService.exists(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId()));
        if (exist) {
            return;
        }
        adAccountService.save(account);
        log.info("广告户{}创建成功", account.getPlatformAdId());
    }

    @Async
    @EventListener
    public void update(AdAccountUpdateEvent event) {
        AdAccountDO account = (AdAccountDO)event.getSource();
        if (account.getId() == null) {
            if (StringUtils.isNotBlank(account.getPlatformAdId())) {
                adAccountService.update(account, Wrappers.<AdAccountDO>lambdaUpdate()
                    .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId()));
            }
        } else {
            adAccountService.updateById(account);
        }
    }

    @Async
    @EventListener
    public void changeStatus(AdAccountStatusChangeEvent event) {
        AdAccountDO account = (AdAccountDO)event.getSource();
        if (account.getAccountStatus() == null || account.getPlatformAdId() == null) {
            return;
        }
        RLock rLock = RedisUtils.getLock(account.getPlatformAdId());
        if (rLock.tryLock()) {
            try {
                if (account.getAccountStatus().equals(AdAccountStatusEnum.NORMAL)) {
                    boolean flag = adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                        .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL)
                        .set(AdAccountDO::getBanTime, null)
                        .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId())
                        .set(AdAccountDO::getUsable, true)
                        .set(AdAccountDO::getUnusableReason, null)
                        .ne(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL)
                        .and(v -> v.in(AdAccountDO::getUnusableReason, List.of(AdAccountUnusableReasonEnum.SELF_BANNED, AdAccountUnusableReasonEnum.BM_BANNED))
                            .or()
                            .isNull(AdAccountDO::getUnusableReason)));
                    if (flag) {
                        log.info("广告户{}恢复正常", account.getPlatformAdId());
                        // 检测是否有申诉订单
                        AppealOrderDO appealOrderDO = appealOrderService.getOne(Wrappers.<AppealOrderDO>lambdaQuery()
                            .eq(AppealOrderDO::getPlatformAdId, account.getPlatformAdId())
                            .in(AppealOrderDO::getStatus, List.of(AppealOrderStatusEnum.WAIT, AppealOrderStatusEnum.HANDLING)));
                        if (appealOrderDO != null) {
                            appealOrderService.cancelOrder(appealOrderDO.getId());
                            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                .chatId(telegramChatIdConfig.getMonitorChatId())
                                .text("广告户 %s %s恢复正常✅，请及时处理".formatted(account.getPlatformAdId(), "停用"))
                                .build()));
                        }
                    }
                } else if (account.getAccountStatus().equals(AdAccountStatusEnum.BANNED)) {
                    boolean flag = adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                        .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED)
                        .set(AdAccountDO::getBanTime, LocalDateTime.now())
                        .set(AdAccountDO::getUnusableReason, account.getUnusableReason() == null
                            ? AdAccountUnusableReasonEnum.SELF_BANNED
                            : account.getUnusableReason())
                        .set(AdAccountDO::getUsable, false)
                        .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId())
                        .ne(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED));
                    if (flag) {
                        log.info("广告户{}被封禁", account.getPlatformAdId());
                        // 更新坑位状态
                        businessManagerItemService.update(Wrappers.<BusinessManagerItemDO>lambdaUpdate()
                            .set(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.BANNED)
                            .set(BusinessManagerItemDO::getBanTime, LocalDateTime.now())
                            .eq(BusinessManagerItemDO::getPlatformAdId, account.getPlatformAdId())
                            .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.NORMAL));
                        AdAccountDO adAccountDO = adAccountService.getByPlatformAdId(account.getPlatformAdId());
                        if (adAccountDO.getSaleStatus()
                            .equals(AdAccountSaleStatusEnum.SALT) || adAccountDO.getKeepStatus()
                            .equals(AdAccountKeepStatusEnum.SUCCESS)) {
                            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                .chatId(telegramChatIdConfig.getAdAccountNotifyChatId())
                                .text("广告户 %s 检测到封禁❌，请及时处理".formatted(account.getPlatformAdId()))
                                .build()));
                            // 提交申诉判断
                            boolean needAppeal = false;
                            if (adAccountDO.getAppealStatus()
                                .equals(AdAccountAppealStatusEnum.WAIT) || adAccountDO.getAppealStatus()
                                .equals(AdAccountAppealStatusEnum.SUCCESS)) {
                                if (account.getUnusableReason() == null) {
                                    needAppeal = true;
                                } else if (!account.getUnusableReason()
                                    .equals(AdAccountUnusableReasonEnum.BM_KICKED_BY_PERMISSION) && !account.getUnusableReason()
                                    .equals(AdAccountUnusableReasonEnum.BM_KILL)) {
                                    needAppeal = true;
                                }
                                if (!adAccountDO.getBmItemType()
                                    .equals(BusinessManagerTypeEnum.BM5.getLongValue()) && !adAccountDO.getBmItemType()
                                    .equals(BusinessManagerTypeEnum.BM10.getLongValue())) {
                                    needAppeal = false;
                                }
                            }
                            if (needAppeal) {
                                AdAccountApplyAppealEvent applyAppealEvent = new AdAccountApplyAppealEvent(account.getPlatformAdId());
                                SpringUtil.publishEvent(applyAppealEvent);
                            } else {
                                adAccountService.withdrawAllCards(adAccountDO.getPlatformAdId());
                                adAccountService.updateCardStatus(adAccountDO.getPlatformAdId(), false);
                            }
                        }
                    }
                } else if (account.getAccountStatus().equals(AdAccountStatusEnum.ABNORMAL)) {
                    boolean flag = adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                        .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.ABNORMAL)
                        .set(AdAccountDO::getBanTime, LocalDateTime.now())
                        .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId())
                        .ne(AdAccountDO::getAccountStatus, AdAccountStatusEnum.ABNORMAL));
                    if (flag) {
                        AdAccountDO adAccountDO = adAccountService.getByPlatformAdId(account.getPlatformAdId());
                        if (adAccountDO.getClearStatus()
                            .equals(AdAccountClearStatusEnum.WAIT) && adAccountDO.getSaleStatus()
                            .equals(AdAccountSaleStatusEnum.SALT)) {
                            String customerName = "";
                            AdAccountOrderDO accountOrderDO = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                                .eq(AdAccountOrderDO::getAdAccountId, account.getPlatformAdId())
                                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
                            if (accountOrderDO != null) {
                                CustomerDO customerDO = customerService.getById(accountOrderDO.getCustomerId());
                                customerName = customerDO.getName();
                            }
                            log.info("广告户{}检测到支付问题", account.getPlatformAdId());
                            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                .chatId(telegramChatIdConfig.getMonitorChatId())
                                .text("%s广告户 %s 检测到支付问题⭕⭕⭕，请及时检查账户".formatted(customerName, account.getPlatformAdId()))
                                .build()));
                        }
                    }
                } else {
                    adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                        .set(AdAccountDO::getAccountStatus, account.getAccountStatus())
                        .eq(AdAccountDO::getPlatformAdId, account.getPlatformAdId()));
                }
            } finally {
                rLock.unlock();
            }
        } else {
            log.info("广告户{}状态改变事件已被其他线程处理", account.getPlatformAdId());
        }
    }

    @Async
    @EventListener
    public void bindCard(AdAccountBindCardEvent event) {
        AdAccountBindCardEvent.AdAccountBindCardModel model = (AdAccountBindCardEvent.AdAccountBindCardModel)event.getSource();
        List<AdAccountCardDO> cardList = adAccountCardService.listByPlatformAdId(model.getPlatformAdId());
        AdAccountCardDO exist = cardList.stream()
            .filter(v -> v.getFullCardNumber().equals(model.getCardNumber()))
            .findFirst()
            .orElse(null);
        if (exist != null) {
            return;
        }
        String last4 = StringUtils.substring(model.getCardNumber(), model.getCardNumber()
            .length() - 4, model.getCardNumber().length());
        AdAccountCardDO existFuzzy = cardList.stream()
            .filter(v -> v.getFuzzyCardNumber().endsWith(last4))
            .findFirst()
            .orElse(null);
        if (existFuzzy != null) {
            adAccountCardService.update(Wrappers.<AdAccountCardDO>lambdaUpdate()
                .set(AdAccountCardDO::getFullCardNumber, model.getCardNumber())
                .set(AdAccountCardDO::getPlatform, model.getCardPlatform())
                .eq(AdAccountCardDO::getId, existFuzzy.getId()));
        } else {
            AdAccountCardDO adAccountCard = new AdAccountCardDO();
            adAccountCard.setPlatform(model.getCardPlatform());
            adAccountCard.setFullCardNumber(model.getCardNumber());
            adAccountCard.setPlatformAdId(model.getPlatformAdId());
            adAccountCardService.save(adAccountCard);
        }
        CardDO cardDO = cardService.getByCardNumberByCache(model.getCardNumber().trim());
        if (cardDO != null) {
            cardService.update(Wrappers.<CardDO>lambdaUpdate()
                .set(CardDO::getPlatformAdId, model.getPlatformAdId())
                .set(CardDO::getRemark, model.getPlatformAdId())
                            .set(CardDO::getHasUsed, true)
                .eq(CardDO::getId, cardDO.getId()));
            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
            cardDO.setRemark(model.getPlatformAdId());
            cardOpsStrategy.updateRemark(cardDO);
        }
    }

    @Async
    @EventListener
    public void queryPayment(AdAccountPaymentQueryEvent event) {
        BillingPaymentAccountModel paymentAccountModel = (BillingPaymentAccountModel)event.getSource();
        if (paymentAccountModel.getPaymentMethods() == null || paymentAccountModel.getPaymentMethods().isEmpty()) {
            return;
        }
        List<AdAccountCardDO> cardList = adAccountCardService.listByPlatformAdId(paymentAccountModel.getPlatformAdId());
        for (AdAccountCardDO cardDO : cardList) {
            BillingPaymentAccountModel.BillingPaymentMethod paymentMethod = paymentAccountModel.getPaymentMethods()
                .stream()
                .filter(v -> cardDO.getFullCardNumber().endsWith(v.getLast4()) || cardDO.getFuzzyCardNumber()
                    .endsWith(v.getLast4()))
                .findFirst()
                .orElse(null);
            if (paymentMethod == null) {
                AdAccountCardDO update = new AdAccountCardDO();
                update.setId(cardDO.getId());
                update.setIsRemove(true);
                update.setIsDefault(false);
                adAccountCardService.updateById(update);
            } else {
                AdAccountCardDO update = new AdAccountCardDO();
                update.setId(cardDO.getId());
                update.setIsRemove(false);
                update.setIsDefault(paymentMethod.isPrimary());
                adAccountCardService.updateById(update);
            }
        }
    }

    @Async
    @EventListener
    public void applyAppeal(AdAccountApplyAppealEvent event) {
        String platformAdId = (String)event.getSource();
        AppealOrderDO existOrder = appealOrderService.getOne(Wrappers.<AppealOrderDO>lambdaQuery()
            .eq(AppealOrderDO::getPlatformAdId, platformAdId)
            .in(AppealOrderDO::getStatus, List.of(AppealOrderStatusEnum.WAIT, AppealOrderStatusEnum.HANDLING)));
        if (existOrder != null) {
            return;
        }
        AppealOrderDO order = new AppealOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("SS"));
        AdAccountOrderDO adAccountOrder = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getAdAccountId, platformAdId)
            .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        if (adAccountOrder != null) {
            order.setCustomerId(adAccountOrder.getCustomerId());
        }
        order.setPlatformAdId(platformAdId);
        order.setStatus(AppealOrderStatusEnum.WAIT);
        appealOrderService.save(order);
        AppealOrderCreateEvent createEvent = new AppealOrderCreateEvent(order.getId());
        SpringUtil.publishEvent(createEvent);
    }

    @Async
    @EventListener
    public void adAccountStolen(AdAccountStolenEvent event) {
        String platformAdId = (String)event.getSource();
        AdAccountDO accountDO = adAccountService.getByPlatformAdId(platformAdId);
        if (accountDO.getSaleStatus().equals(AdAccountSaleStatusEnum.SALT) && accountDO.getUsable()) {
            String message;
            String customerName = adAccountOrderMapper.getCustomerName(platformAdId);
            if (event.isLost()) {
                message = BotUtils.createAdPermissionLostMessage(platformAdId, customerName);
            } else {
                message = BotUtils.createAdSpentCapLostMessage(platformAdId, customerName);
            }
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getMonitorChatId())
                .text(message)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getFinanceChatId())
                .text(message)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            AdAccountDO statusChange = new AdAccountDO();
            statusChange.setPlatformAdId(accountDO.getPlatformAdId());
            statusChange.setAccountStatus(AdAccountStatusEnum.BANNED);
            statusChange.setUsable(false);
            statusChange.setUnusableReason(AdAccountUnusableReasonEnum.BM_KICKED_BY_PERMISSION);
            AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
            SpringUtil.publishEvent(adAccountStatusChangeEvent);
        }
    }

    @Async
    @EventListener
    public void adsPowerEnvReport(AdsPowerEnvEvent event) {
        AdsPowerOpenReq req = (AdsPowerOpenReq)event.getSource();
        if (StringUtils.isBlank(req.getUser_id()) || StringUtils.isBlank(req.getSerial_number())) {
            return;
        }
        RLock rLock = RedisUtils.getLock(req.getUser_id());
        if (rLock.tryLock()) {
            try {
                boolean exist = adsPowerService.exists(Wrappers.<AdsPowerDO>lambdaQuery()
                    .eq(AdsPowerDO::getUserId, req.getUser_id()));
                if (!exist) {
                    AdsPowerDO save = new AdsPowerDO();
                    save.setUserId(req.getUser_id());
                    save.setSerialNumber(req.getSerial_number());
                    String remark = "";
                    if (StringUtils.isNotBlank(req.getRemark())) {
                        remark = StrUtil.sub(req.getRemark(), 0, 200);
                    }
                    save.setRemark(remark);
                    adsPowerService.save(save);
                }
            } finally {
                rLock.unlock();
            }
        }
    }
}
