/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.FbChannelMapper;
import top.continew.admin.biz.model.entity.FbChannelDO;
import top.continew.admin.biz.model.query.FbChannelQuery;
import top.continew.admin.biz.model.req.FbChannelReq;
import top.continew.admin.biz.model.resp.FbChannelDetailResp;
import top.continew.admin.biz.model.resp.FbChannelResp;
import top.continew.admin.biz.service.FbChannelService;

/**
 * FB账号渠道业务实现
 *
 * <AUTHOR>
 * @since 2025/01/06 11:37
 */
@Service
@RequiredArgsConstructor
public class FbChannelServiceImpl extends BaseServiceImpl<FbChannelMapper, FbChannelDO, FbChannelResp, FbChannelDetailResp, FbChannelQuery, FbChannelReq> implements FbChannelService {}