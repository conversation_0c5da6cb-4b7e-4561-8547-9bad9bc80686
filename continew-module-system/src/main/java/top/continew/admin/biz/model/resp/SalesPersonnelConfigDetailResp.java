package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 商务人员配置数据详情信息
 *
 * <AUTHOR>
 * @since 2025/08/21 13:46
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务人员配置数据详情信息")
public class SalesPersonnelConfigDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @ExcelProperty(value = "关联商务")
    private Long businessUserId;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    @ExcelProperty(value = "统计月份")
    private LocalDate salesDate;

    /**
     * 配置数据
     */
    @Schema(description = "配置数据")
    @ExcelProperty(value = "配置数据")
    private String data;
}