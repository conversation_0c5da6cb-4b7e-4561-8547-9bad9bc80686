package top.continew.admin.biz.controller;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.TransactionUserQuery;
import top.continew.admin.biz.model.req.TransactionUserReq;
import top.continew.admin.biz.model.resp.TransactionUserDetailResp;
import top.continew.admin.biz.model.resp.TransactionUserResp;
import top.continew.admin.biz.service.TransactionUserService;

/**
 * 交易对象管理 API
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Tag(name = "交易对象管理 API")
@RestController
@CrudRequestMapping(value = "/biz/transactionUser", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class TransactionUserController extends BaseController<TransactionUserService, TransactionUserResp, TransactionUserDetailResp, TransactionUserQuery, TransactionUserReq> {}