package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdProductStatDO;
import top.continew.admin.biz.model.query.AdProductStatQuery;
import top.continew.admin.biz.model.query.CampaignDataQuery;
import top.continew.admin.biz.model.req.AdProductStatItemReq;
import top.continew.admin.biz.model.req.AdProductStatReq;
import top.continew.admin.biz.model.resp.AdProductStatDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatResp;
import top.continew.admin.biz.model.resp.CampaignPerformanceResp;
import top.continew.admin.biz.model.resp.ToufangCustomerDailyReportResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 产品日报业务接口
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
public interface AdProductStatService extends BaseService<AdProductStatResp, AdProductStatDetailResp, AdProductStatQuery, AdProductStatReq>, IService<AdProductStatDO> {

    List<CampaignPerformanceResp> campaignData(CampaignDataQuery query);

    /**
     * 批量添加广告户消耗
     *
     * @param id
     * @param items
     */
    void batchAddItem(Long id, List<AdProductStatItemReq> items);


    List<ToufangCustomerDailyReportResp> getCustomerDailyReport(Long customerId);

}