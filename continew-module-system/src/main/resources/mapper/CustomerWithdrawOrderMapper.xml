<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerWithdrawOrderMapper">
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp">
        WITH customer_transfer as (select customer_id, SUM(amount) as amount
                                   from biz_customer_balance_record
                                   where type = 1
                                   group by customer_id),
             customer_product_data as (select customer_id,
                                              COALESCE(SUM(spend), 0)                           as spend,
                                              COALESCE(SUM(fee), 0)                             as fee,
                                              (COALESCE(SUM(spend), 0) + COALESCE(SUM(fee), 0)) as total_spend
                                       from biz_ad_product_stat
                                       group by customer_id)
        select cwo.id,
               cwo.order_no,
               cwo.status,
               cwo.customer_id,
               cwo.expected_refund_time,
               cwo.remark,
               cwo.create_user,
               cwo.audit_time,
               cwo.actual_refund_time,
               cwo.actual_refund_amount,
               cwo.audit_remark,
               cwo.create_time,
               cwo.auditor,
               c.name    as customer_name,
               if(c.business_type = 1,c.balance, COALESCE(ct.amount, 0) - COALESCE(cpd.total_spend, 0))  as customer_balance
        from biz_customer_withdraw_order cwo
            left join biz_customer c on c.id = cwo.customer_id
            left join customer_transfer ct on ct.customer_id = c.id
            left join customer_product_data cpd on cpd.customer_id = c.id
        <where>
            <if test="null != query.customerId">
                and cwo.customer_id = #{query.customerId}
            </if>
            <if test="null != query.orderNo">
                and cwo.order_no = #{query.orderNo}
            </if>
            <if test="null != query.status">
                and cwo.status = #{query.status}
            </if>
            <if test="null != query.createUser">
                and cwo.create_user = #{createUser}
            </if>

            <if test="null != query.createTime and query.createTime.length!=0">
                and cwo.create_time &gt;= #{query.createTime[0]}
            </if>

            <if test="null != query.createTime and query.createTime.length>1">
                and cwo.create_time &lt;= #{query.createTime[1]}
            </if>

            <if test="null != query.expectedRefundTime and query.expectedRefundTime.length!=0">
                and cwo.expected_refund_time &gt;= #{query.expectedRefundTime[0]}
            </if>

            <if test="null != query.expectedRefundTime and query.expectedRefundTime.length>1">
                and cwo.expected_refund_time &lt;= #{query.expectedRefundTime[1]}
            </if>
            <if test="null != query.businessType">
                and c.business_type = #{query.businessType}
            </if>
        </where>
        ORDER BY cwo.create_time DESC
    </select>
    <select id="getBalance" resultType="java.math.BigDecimal">
        SELECT IF(c.business_type = 1, c.balance,
                  COALESCE(SUM(IF(bcbr.type = 1, bcbr.amount, 0)), 0) -
                  COALESCE(SUM(COALESCE(baps.spend, 0) + COALESCE(baps.fee, 0)), 0)) AS customer_balance
        FROM biz_customer c
                 LEFT JOIN
             biz_customer_balance_record bcbr ON bcbr.customer_id = c.id AND bcbr.type = 1
                 LEFT JOIN
             biz_ad_product_stat baps ON baps.customer_id = c.id
        WHERE c.id = #{id}
        GROUP BY c.id, c.business_type, c.balance
    </select>
</mapper>