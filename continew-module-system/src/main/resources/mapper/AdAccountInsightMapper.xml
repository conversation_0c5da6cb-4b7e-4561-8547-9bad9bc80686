<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdAccountInsightMapper">
    <select id="selectInactiveAccounts" resultType="java.lang.String">
        SELECT ad_account_id
        FROM biz_ad_account_insight
        WHERE stat_date BETWEEN CURDATE() - INTERVAL 7 DAY AND CURDATE()
        AND ad_account_id IN (
        <foreach item="id" index="index" collection="list" open="" separator="," close="">
            #{id}
        </foreach>
        )
        GROUP BY ad_account_id
        <![CDATA[
        HAVING SUM(spend) <= 100
        ]]>
    </select>



    <select id="sumSpendByDateRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(spend), 0) as total_spend
        FROM biz_ad_account_insight
        WHERE ad_account_id = #{platformAdId}
          AND stat_date >= #{startDate}
          AND stat_date &lt;= #{endDate}
    </select>
    <select id="selectDashboardOverviewSpent"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT IFNULL(SUM(bai.spend), 0) AS total,
               IFNULL(SUM(CASE
                              WHEN bai.stat_date >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                                  AND bai.stat_date &lt; CURDATE()
                                  THEN bai.spend
                   END), 0)              AS today,
               IFNULL(SUM(CASE
                              WHEN bai.stat_date >= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
                                  AND bai.stat_date &lt; DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                                  THEN bai.spend
                   END), 0)              AS yesterday
        FROM biz_ad_account_insight bai
        WHERE bai.customer_id is not null
    </select>
    <select id="selectListDashboardAnalysisSpent"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(stat_date, '%Y-%m') AS name,
        ifnull(sum(spend), 0) AS value
        FROM biz_ad_account_insight
        WHERE customer_id is not null AND DATE_FORMAT(stat_date, '%Y-%m') IN
        <foreach collection="months" item="month" separator="," open="(" close=")">
            #{month}
        </foreach>
        GROUP BY name
        ORDER BY name
    </select>
    <select id="selectLast" resultType="top.continew.admin.biz.model.entity.AdAccountInsightDO">
        SELECT *
            FROM (
                SELECT
                    baai.*,
                    ROW_NUMBER() OVER(PARTITION BY baai.ad_account_id,baai.customer_id ORDER BY baai.id DESC) as rn
                FROM
                    biz_ad_account_insight baai
                WHERE
                    baai.ad_account_id IN
                    <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND baai.customer_id IS NOT NULL
            ) AS ranked_orders
        WHERE
            rn = 1
    </select>

</mapper>