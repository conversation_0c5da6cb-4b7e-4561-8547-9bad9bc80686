<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.SettleOrderMapper">
    <select id="customPage" resultType="top.continew.admin.biz.model.resp.SettleOrderResp">
        select bso.customer_id,bso.total_spent,bso.settle_spent,bso.settle_time,c.name as customerName
        from biz_settle_order bso
                 left join biz_customer c on bso.customer_id = c.id
            ${ew.customSqlSegment}
    </select>
</mapper>