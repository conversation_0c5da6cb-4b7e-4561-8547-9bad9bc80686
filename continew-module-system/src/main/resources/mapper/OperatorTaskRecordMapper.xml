<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.OperatorTaskRecordMapper">
    <select id="summaryStat" resultType="top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp">
        select  type , sum(num) as count
        from biz_operator_task_record botr
            ${ew.customSqlSegment}
        group by type
    </select>
    <select id="selectOperationTaskStat" resultType="top.continew.admin.biz.model.resp.operationTaskStatResp">
        select su.id as userId, su.nickname as username, sum(botr.num) as total
            from biz_operator_task_record botr
                left join sys_user su on botr.create_user = su.id
            <where>
                <if test="query.createUser != null">
                    and botr.create_user = #{query.createUser}
                </if>
                <if test="query.createTime != null and query.createTime.length == 2">
                    and botr.create_time between #{query.createTime[0]} and #{query.createTime[1]}
                </if>
            </where>
            GROUP BY su.id ,su.nickname
    </select>
</mapper>