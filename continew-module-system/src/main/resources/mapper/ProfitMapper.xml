<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ProfitMapper">
    <sql id="base_query_sql">
        select p.*, pt.name as type_name, tu.name as transaction_user_name
        from biz_profit p
                 left join biz_profit_type pt on pt.id = p.type
                 left join biz_transaction_user tu on tu.id = p.transaction_user_id ${ew.customSqlSegment}
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.ProfitResp">
        <include refid="base_query_sql"/>
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.ProfitDetailResp">
        <include refid="base_query_sql"/>
    </select>
</mapper>