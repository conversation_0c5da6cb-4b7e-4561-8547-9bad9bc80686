<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.biz.mapper.analyze.CustomerAdAccountBannedStatMapper">

    <select id="selectPageCustomerBase" resultType="top.continew.admin.biz.model.resp.CustomerBanStatsResp">
        SELECT
        c.id AS customerId,
        c.NAME AS customerName,
        sub.totalCount
        FROM
        biz_customer c
        JOIN (
        SELECT
        customer_id,
        COUNT( DISTINCT ad_account_id) AS totalCount
        FROM
        biz_ad_account_order aao
        <where>
            AND aao.status IN (3, 5)
            <if test="query.startTime != null and query.endTime != null">
                AND aao.finish_time BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        GROUP BY
        aao.customer_id
        ) AS sub ON c.id = sub.customer_id
        <where>
            <if test="query.customerIds != null and query.customerIds.size() > 0">
                AND c.id IN
                <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
        </where>
        ORDER BY c.id DESC
    </select>
    <select id="selectInfoData" resultType="top.continew.admin.biz.model.resp.CustomerBanStatsResp">
        -- 查询有相关条件的广告户ID
        WITH adAccountOrders AS (SELECT
        id,
        customer_id,
        ad_account_id,
        total_spent,
        first_start_campaign_time
        FROM(SELECT
        aao.*,
        ROW_NUMBER() OVER (
        PARTITION BY <if test="customerId!=null">aao.customer_id,</if> aao.ad_account_id
        ORDER BY aao.id DESC
        ) as rn
        FROM
        biz_ad_account_order aao
        <if test="cardHard !=null">
            LEFT JOIN biz_card c ON c.platform_ad_id = aao.ad_account_id
        </if>
        <if test="timezone !=null">
            LEFT JOIN biz_ad_account a on a.platform_ad_id = aao.ad_account_id
        </if>
        WHERE
        aao.`status` IN ( 3, 5 )
        <if test="customerId!=null">
            AND customer_id = #{customerId}
        </if>
        <if test="adAccountIds != null and adAccountIds.size() > 0">
            AND aao.ad_account_id IN
            <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null and endTime != null">
            AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="cardHard != null">
            AND c.card_number LIKE CONCAT(#{cardHard},'%')
        </if>
        <if test="timezone != null">
            AND a.timezone = #{timezone}
        </if>
        <if test="oneDollar != null and oneDollar == true">
            AND aao.is_one_dollar = TRUE
        </if>
        ) AS RankedOrders
        WHERE
        rn = 1)
        SELECT -- 下户总数
        COUNT(ao.id) AS totalCount,
        -- 总封禁数
        COUNT(CASE WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        THEN 1 END) AS totalBanCount,
        -- 未使用被封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ao.`first_start_campaign_time` IS NULL
        THEN 1 END) AS unusedBanCount,
        -- 未消耗封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND IFNULL( ao.total_spent, 0 ) = 0 AND ao.`first_start_campaign_time` IS NOT NULL
        THEN 1 END) AS unconsumedBanCount,
        -- 已消耗封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ao.total_spent > 0 AND ao.`first_start_campaign_time` IS NOT NULL
        THEN 1 END) AS consumedBanCount
        FROM adAccountOrders ao
        LEFT JOIN biz_ad_account a ON a.platform_ad_id = ao.ad_account_id
    </select>
    <select id="getCustomerDashboardStats" resultType="top.continew.admin.biz.model.resp.CustomerBanStatsResp">
        WITH adAccountOrders AS (
            SELECT
                id,
                customer_id,
                ad_account_id,
                total_spent,
                first_start_campaign_time
            FROM
                (
                    SELECT
                        aao.*,
                        ROW_NUMBER() OVER ( PARTITION BY aao.customer_id, aao.ad_account_id ORDER BY aao.id DESC ) AS rn
                    FROM
                        biz_ad_account_order aao
                    WHERE
                        aao.`status` IN ( 3, 5 )
                    <if test="customerIds != null and customerIds.size() > 0">
                        AND aao.customer_id IN
                        <foreach item="item" index="index" collection="customerIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="startTime != null and endTime != null">
                        AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
                    </if>
                ) AS RankedOrders
            WHERE
                rn = 1
        )
        SELECT
            -- 总下户数
            COUNT( ao.id ) AS totalCount,
            -- 总封禁数
            COUNT(CASE WHEN a.usable = FALSE
            <if test="banType != null and banType.size() > 0">
                AND a.unusable_reason IN
                <foreach item="item" collection="banType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            THEN 1 END) AS totalBanCount,
            -- 未使用被封禁数
            COUNT(CASE
            WHEN a.usable = FALSE
            <if test="banType != null and banType.size() > 0">
                AND a.unusable_reason IN
                <foreach item="item" collection="banType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND ao.first_start_campaign_time IS NULL
            THEN 1 END) AS unusedBanCount,
            -- 未消耗封禁数
            COUNT(CASE
            WHEN a.usable = FALSE
            <if test="banType != null and banType.size() > 0">
                AND a.unusable_reason IN
                <foreach item="item" collection="banType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND IFNULL( ao.total_spent, 0 ) = 0 AND ao.first_start_campaign_time IS NOT NULL
            THEN 1 END) AS unconsumedBanCount,
            -- 已消耗封禁数
            COUNT(CASE
            WHEN a.usable = FALSE
            <if test="banType != null and banType.size() > 0">
                AND a.unusable_reason IN
                <foreach item="item" collection="banType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND ao.total_spent > 0 AND ao.first_start_campaign_time IS NOT NULL
            THEN 1 END) AS consumedBanCount
        FROM
            adAccountOrders ao
        JOIN biz_ad_account a ON a.platform_ad_id = ao.ad_account_id
    </select>
    <select id="getAdAccountDashboardStats"
            resultType="top.continew.admin.biz.model.resp.CustomerBanStatsResp">
        WITH adAccountOrders AS (
            SELECT
                id,
                customer_id,
                ad_account_id,
                total_spent,
                first_start_campaign_time
            FROM
                (
                SELECT
                    aao.*,
                    ROW_NUMBER() OVER ( PARTITION BY aao.ad_account_id ORDER BY aao.id DESC ) AS rn
                FROM
                    biz_ad_account_order aao
                WHERE
                    aao.`status` IN ( 3, 5 )
                    <if test="startTime != null and endTime != null">
                        AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
                    </if>
                ) AS RankedOrders
                WHERE
                    rn = 1
            )
        SELECT
        -- 总下户数
        COUNT( ao.id ) AS totalCount,
        -- 总封禁数
        COUNT(CASE WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        THEN 1 END) AS totalBanCount,
        -- 未使用被封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ao.first_start_campaign_time IS NULL
        THEN 1 END) AS unusedBanCount,
        -- 未消耗封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND IFNULL( ao.total_spent, 0 ) = 0 AND ao.first_start_campaign_time IS NOT NULL
        THEN 1 END) AS unconsumedBanCount,
        -- 已消耗封禁数
        COUNT(CASE
        WHEN a.usable = FALSE
        <if test="banType != null and banType.size() > 0">
            AND a.unusable_reason IN
            <foreach item="item" collection="banType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ao.total_spent > 0 AND ao.first_start_campaign_time IS NOT NULL
        THEN 1 END) AS consumedBanCount
        FROM
        adAccountOrders ao
        JOIN biz_ad_account a ON a.platform_ad_id = ao.ad_account_id
    </select>
</mapper>