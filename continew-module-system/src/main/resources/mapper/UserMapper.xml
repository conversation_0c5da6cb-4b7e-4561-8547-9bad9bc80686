<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.system.mapper.UserMapper">

    <sql id="selectUser">
        SELECT
            t1.id,
            t1.create_user,
            t1.create_time,
            t1.update_user,
            t1.update_time,
            t1.username,
            t1.nickname,
            t1.password,
            t1.gender,
            t1.email,
            t1.phone,
            t1.avatar,
            t1.description,
            t1.status,
            t1.is_system,
            t1.pwd_reset_time,
            t1.dept_id,
            t1.job_rank,
            t1.telegram_id,
            t2.name AS deptName
        FROM sys_user AS t1
        LEFT JOIN sys_dept AS t2 ON t2.id = t1.dept_id
    </sql>

    <select id="selectUserPage" resultType="top.continew.admin.system.model.resp.user.UserDetailResp">
        <include refid="selectUser" />
        ${ew.customSqlSegment}
    </select>

    <select id="selectUserList" resultType="top.continew.admin.system.model.resp.user.UserDetailResp">
        <include refid="selectUser" />
        ${ew.customSqlSegment}
    </select>

    <select id="selectCountByEmail" resultType="java.lang.Long">
        SELECT count(*)
        FROM sys_user
        WHERE email = #{email}
          <if test="id != null">
            AND id != #{id}
          </if>
    </select>

    <select id="selectCountByPhone" resultType="java.lang.Long">
        SELECT count(*)
        FROM sys_user
        WHERE phone = #{phone}
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>
    <select id="selectOperationsStat" resultType="top.continew.admin.biz.model.resp.OperationsStatResp">
        WITH
        AuthComplete AS (
        SELECT
        baao.handle_user AS user_id,
        COUNT(*) AS authCompleteCount
        FROM biz_ad_account_order baao
        WHERE baao.STATUS = 3
        <if test="date != null and date.length > 0">
            AND baao.finish_time BETWEEN #{date[0]} AND #{date[1]}
        </if>
        GROUP BY baao.handle_user
        ),
        RechargeComplete AS (
        SELECT
        bro.handle_user AS user_id,
        COUNT(*) AS rechargeCompleteCount
        FROM biz_recharge_order bro
        WHERE bro.STATUS = 3
        <if test="date != null and date.length > 0">
            AND bro.finish_time BETWEEN #{date[0]} AND #{date[1]}
        </if>
        GROUP BY bro.handle_user
        ),
        ClearComplete AS (
        SELECT
        bco.handle_user AS user_id,
        COUNT(*) AS clearCompleteCount
        FROM biz_clear_order bco
        WHERE bco.STATUS = 3
        <if test="date != null and date.length > 0">
            AND bco.finish_time BETWEEN #{date[0]} AND #{date[1]}
        </if>
        GROUP BY bco.handle_user
        ),
        ClaimCount AS (
        SELECT
        baabl.create_user AS user_id,
        COUNT(*) AS claimCount
        FROM biz_ad_account_browser_log baabl
        WHERE baabl.label = '认领广告'
        <if test="date != null and date.length > 0">
            AND baabl.create_time BETWEEN #{date[0]} AND #{date[1]}
        </if>
        GROUP BY baabl.create_user
        )
        SELECT
        su.nickname AS handleUserName,
        COALESCE(ac.authCompleteCount, 0) AS authCompleteCount,
        COALESCE(rc.rechargeCompleteCount, 0) AS rechargeCompleteCount,
        COALESCE(cc.clearCompleteCount, 0) AS clearCompleteCount,
        COALESCE(cl.claimCount, 0) AS claimCount
        FROM sys_user su
        LEFT JOIN AuthComplete ac ON su.id = ac.user_id
        LEFT JOIN RechargeComplete rc ON su.id = rc.user_id
        LEFT JOIN ClearComplete cc ON su.id = cc.user_id
        LEFT JOIN ClaimCount cl ON su.id = cl.user_id
        WHERE su.dept_id = 664896817443426363;
    </select>

    <select id="selectUserDictList" resultType="top.continew.admin.system.model.resp.user.UserDetailResp">
        SELECT DISTINCT
            u.id,
            u.nickname
        FROM sys_user u
        <if test="query.roleCodePrefix != null and query.roleCodePrefix != ''">
            LEFT JOIN sys_user_role ur ON u.id = ur.user_id
            LEFT JOIN sys_role r ON r.id = ur.role_id
        </if>
        WHERE 1=1
        <if test="query.status != null">
            AND u.status = #{query.status}
        </if>
        <if test="query.deptId != null">
            AND u.dept_id = #{query.deptId}
        </if>
        <if test="query.userIds != null and query.userIds.size() > 0">
            AND u.id IN
            <foreach collection="query.userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="query.roleCodePrefix != null and query.roleCodePrefix != ''">
            AND r.code LIKE CONCAT(#{query.roleCodePrefix}, '%')
        </if>
        ORDER BY u.id
    </select>
</mapper>