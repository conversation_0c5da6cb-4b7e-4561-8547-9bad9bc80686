<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CampaignInsightMapper">

    <!-- 批量插入或更新广告组成效数据 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO biz_campaign_insight (
            platform_campaign_id, platform_adset_id, ad_account_id, stat_date,
            impressions, clicks, spend, actions_data, conversions_data, conversion_value, conversion_key, create_time, update_time
        ) VALUES
        <foreach collection="insights" item="insight" separator=",">
            (
                #{insight.platformCampaignId},
                #{insight.platformAdsetId},
                #{insight.adAccountId},
                #{insight.statDate},
                #{insight.impressions},
                #{insight.clicks},
                #{insight.spend},
                #{insight.actionsData},
                #{insight.conversionsData},
                #{insight.conversionValue},
                #{insight.conversionKey},
                NOW(),
                NOW()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            impressions = VALUES(impressions),
            clicks = VALUES(clicks),
            spend = VALUES(spend),
            actions_data = VALUES(actions_data),
            conversions_data = VALUES(conversions_data),
            conversion_value = VALUES(conversion_value),
            conversion_key = VALUES(conversion_key),
            update_time = VALUES(update_time)
    </insert>

    <!-- 根据广告户ID和统计日期删除过期数据 -->
    <delete id="deleteByAdAccountIdAndStatDateNot">
        DELETE FROM biz_campaign_insight
        WHERE ad_account_id = #{adAccountId}
        AND stat_date != #{statDate}
    </delete>

</mapper>