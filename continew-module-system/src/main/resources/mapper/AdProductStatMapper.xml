<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdProductStatMapper">
    <sql id="base_query_sql">
        select ps.*,
               c.name                       as customer_name,
               p.agent_no                   as product_name,
               p.type as product_type,
               (ps.spend + ps.reflow_spend) as total_spend,
               (ps.spend + ps.reflow_spend + ps.fee) as total_cost
        from biz_ad_product_stat ps
                 left join biz_customer c on c.id = ps.customer_id
                 left join biz_ad_product p on p.id = ps.product_id ${ew.customSqlSegment}
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AdProductStatResp">
        <include refid="base_query_sql" />
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AdProductStatDetailResp">
        <include refid="base_query_sql" />
    </select>
    <select id="selectCampaignData" resultType="top.continew.admin.biz.model.resp.CampaignPerformanceResp">
        SELECT platform_id,
               NAME     as campaignName,
               a.STATUS as campaignStatus
        FROM biz_fb_ad_campaigns a
                 LEFT JOIN biz_ad_account_order b ON a.ad_account_id = b.ad_account_id
        WHERE b.customer_id = #{customerId}
          AND b.STATUS = 3
          AND a.ad_account_id = #{adAccountId}
    </select>
    <select id="selectCampaignInsight" resultType="top.continew.admin.biz.model.resp.CampaignInsightDataResp">
        SELECT conversion_key,
               sum(conversion_value) as value,
               sum(spend) as spend
        FROM biz_campaign_insight a
        WHERE a.platform_campaign_id = #{platformId}
          AND a.stat_date = #{statDate}
          AND a.spend != 0.00
        GROUP BY conversion_key
    </select>
    <select id="getCustomerDailyReport"
            resultType="top.continew.admin.biz.model.resp.ToufangCustomerDailyReportResp">
        select ps.*,
               p.agent_no                            as product_name,
               p.type                                as product_type,
               (ps.spend + ps.reflow_spend + ps.fee) as total_spend
        from biz_ad_product_stat ps
                 left join biz_ad_product p on p.id = ps.product_id where ps.customer_id = #{customerId} order by ps.stat_date
    </select>

</mapper>