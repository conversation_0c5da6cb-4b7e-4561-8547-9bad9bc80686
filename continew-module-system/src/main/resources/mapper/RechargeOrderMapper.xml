<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.RechargeOrderMapper">
    <sql id="selectRechargeOrder">
        SELECT o.*,
               c.name                                                 AS customer_name,
               c.balance                                              AS customer_balance,
               a.bm_id                                                AS bm_id,
               COALESCE(a.spend_cap, 0)                               AS spend_cap,
               u.nickname                                             AS handle_user_name,
               concat_ws(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, bm.ops_browser, bm.reserve_browser, bm.reserve_browser_bak,
                         bm.observe_browser)                          as browser_no,
               (select COALESCE(
                               SUM(IF(r.type = 3, r.amount, 0)) -
                               SUM(IF(r.type = 5, r.amount, 0)),
                               0
                       )
                from biz_customer_balance_record r
                where r.platform_ad_id = o.platform_ad_id
                  AND r.customer_id = o.customer_id
                  AND r.trans_time > COALESCE((SELECT MAX(br.trans_time)
                                               FROM biz_customer_balance_record br
                                               WHERE br.customer_id = o.customer_id
                                                 AND br.platform_ad_id = a.platform_ad_id
                                                 AND br.type = 4),
                                              '2024-10-01 00:00:00')) as recharge_amount
        FROM biz_recharge_order o
                 LEFT JOIN
             biz_ad_account a ON a.platform_ad_id = o.platform_ad_id
                 LEFT JOIN biz_business_manager bm ON bm.id = COALESCE(a.bm1_id, a.business_manager_id)
                 LEFT JOIN
             sys_user u ON u.id = o.handle_user
                 LEFT JOIN
             biz_customer c ON c.id = o.customer_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.RechargeOrderResp">
        <include refid="selectRechargeOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.RechargeOrderResp">
        <include refid="selectRechargeOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectUserOrderStat" resultType="top.continew.admin.biz.model.resp.UserOrderStatResp">
        select u.nickname, COUNT(*) as num, SUM(o.amount) as amount
        from biz_recharge_order o
        left join sys_user u on u.id = o.handle_user
        where o.status = 3
        <if test="start != null and end != null">
            AND o.create_time BETWEEN #{start} AND #{end}
        </if>
        group by u.nickname order by num desc
    </select>
    <select id="selectLast" resultType="top.continew.admin.biz.model.entity.RechargeOrderDO">
        SELECT *
            FROM (
                SELECT
                    ro.*,
                    ROW_NUMBER() OVER(PARTITION BY ro.platform_ad_id,ro.customer_id ORDER BY ro.id DESC) as rn
                FROM
                    biz_recharge_order ro
                WHERE
                    ro.platform_ad_id IN
                    <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                ) AS ranked_orders
        WHERE
        rn = 1
    </select>
</mapper>