<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdProductMapper">
    <sql id="base_query_sql">
        select p.*, c.name as customer_name from biz_ad_product p left join biz_customer c on c.id = p.customer_id ${ew.customSqlSegment}
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AdProductResp">
        <include refid="base_query_sql" />
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AdProductDetailResp">
        <include refid="base_query_sql"/>
    </select>
</mapper>