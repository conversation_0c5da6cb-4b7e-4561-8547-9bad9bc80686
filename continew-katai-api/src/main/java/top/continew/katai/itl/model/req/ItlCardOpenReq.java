package top.continew.katai.itl.model.req;

import lombok.Data;

/**
 * @version: 1.00.00
 * @description: 创建预算卡（Budget Card）请求参数
 * @date: 2025/8/14 14:08
 */
@Data
public class ItlCardOpenReq {

    /**
     * 卡片类型。默认为 "BudgetCard"
     * 示例: "BudgetCard"
     * 必填
     */
    private String type;

    /**
     * 银行识别号（BIN），卡号的前 6 位或 8 位数字
     * 示例: "123456"
     */
    private String bin;

    /**
     * 要发行的卡数量
     * 示例: 10
     * 必填
     */
    private Integer batchCount;

    private String phoneCode;

    private String phoneNumber;


    /**
     * 自定义使用类型
     * 示例: "Online advertising"
     * 必填
     */
    private String useType;

    /**
     * 卡片模式
     * 示例: "VirtualCard"
     * 可选值: "PhysicalCard", "VirtualCard"
     */
    private String cardMode;

    /**
     * 定制卡片标签或标签，长度≤50
     * 示例: "Marketing"
     */
    private String label;

    /**
     * 预算的 UUID
     * 示例: "a1b2c3d4-e5f6-7890-abcd-1234567890ef"
     * 必填
     */
    private String budgetId;

    /**
     * 持卡人身份证
     * 示例: "c1d2e3f4-5678-90ab-cdef-1234567890ab"
     * 必填
     */
    private String cardholderId;

}
