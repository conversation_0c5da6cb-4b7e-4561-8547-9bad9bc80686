package top.continew.katai.itl.model.req;

import lombok.Data;

/**
 * 创建持卡人请求
 * @version: 1.00.00
 * @description: 创建持卡人请求
 * @date: 2025/3/11 14:13
 */
@Data
public class ItlCreateCardholderReq {
    /**
     * 业务模型
     */
    private String businessModel;
    
    /**
     * 地址信息
     */
    private ItlAddress address;
    
    /**
     * 名字
     */
    private String firstName;
    
    /**
     * 姓氏
     */
    private String lastName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 地址信息
     */
    @Data
    public static class ItlAddress {
        /**
         * 地址行1
         */
        private String addressLine1;
        
        /**
         * 地址行2
         */
        private String addressLine2;
        
        /**
         * 城市
         */
        private String city;
        
        /**
         * 州/省
         */
        private String state;
        
        /**
         * 国家
         */
        private String country;
        
        /**
         * 邮政编码
         */
        private String postalCode;
    }
}