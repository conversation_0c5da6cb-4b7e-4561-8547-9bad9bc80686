package top.continew.katai.itl.model.resp;

import lombok.Data;

import java.util.List;

/**
 * 卡片列表响应
 * @version: 1.00.00
 * @description: 卡片列表响应
 * @date: 2025/3/11 14:13
 */
@Data
public class ItlCardListResp {
    /**
     * 卡片数据列表
     */
    private List<ItlCardDetailResp> data;
    
    /**
     * 总页数
     */
    private Integer pageTotal;

    /**
     * 总记录数
     */
    private Integer total;

}