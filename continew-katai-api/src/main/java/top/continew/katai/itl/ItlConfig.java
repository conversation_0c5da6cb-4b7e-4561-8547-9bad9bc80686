package top.continew.katai.itl;

import lombok.Data;
import top.continew.katai.ThirdConfig;
import top.continew.katai.amz.AmzConfig;

/**
 * @version: 1.00.00
 * @description: Interlace配置类
 * @date: 2025/3/11 14:13
 */
@Data
public class ItlConfig extends ThirdConfig {
    /**
     * 客户端ID
     */
    private String clientId;
    
    /**
     * 客户端密钥
     */
    private String clientSecret;


    /**
     * 获取端点
     *
     * @return 端点
     */
    public String getEndpoint() {
        return endpoint;
    }

    /**
     * 设置端点
     *
     * @param endpoint 端点
     * @return AmzConfig
     */
    public ItlConfig setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }
}
