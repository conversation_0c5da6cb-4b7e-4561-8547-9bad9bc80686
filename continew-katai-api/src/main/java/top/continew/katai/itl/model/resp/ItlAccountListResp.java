package top.continew.katai.itl.model.resp;

import lombok.Data;

import java.util.List;

/**
 * 账户列表响应
 * @version: 1.00.00
 * @description: 账户列表响应
 * @date: 2025/3/11 14:13
 */
@Data
public class ItlAccountListResp {
    /**
     * 账户数据列表
     */
    private List<ItlAccount> data;
    
    /**
     * 总页数
     */
    private Integer pageTotal;
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 账户信息
     */
    @Data
    public static class ItlAccount {
        /**
         * 账户ID
         */
        private String id;
        
        /**
         * 创建时间
         */
        private String createTime;
        
        /**
         * 账户类型
         */
        private String type;
        
        /**
         * 账户状态
         */
        private String status;
        
        /**
         * 账户名称
         */
        private String name;
        
        /**
         * 显示ID
         */
        private String displayId;
        
        /**
         * KYC状态
         */
        private String kycStatus;
        
        /**
         * 卡片KYB状态
         */
        private String cardKybStatus;
    }
}