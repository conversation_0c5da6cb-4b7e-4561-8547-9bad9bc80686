package top.continew.katai.itl.model.resp;

import lombok.Data;

import java.util.List;

/**
 * 持卡人列表响应
 * @version: 1.00.00
 * @description: 持卡人列表响应
 * @date: 2025/3/11 14:13
 */
@Data
public class ItlCardholderListResp {
    /**
     * 持卡人数据列表
     */
    private List<ItlCardholder> data;
    
    /**
     * 持卡人信息
     */
    @Data
    public static class ItlCardholder {
        /**
         * 持卡人ID
         */
        private String id;
        
        /**
         * 持卡人状态
         */
        private String status;
        
        /**
         * 账户ID
         */
        private String accountId;
        
        /**
         * 名字
         */
        private String firstName;
        
        /**
         * 姓氏
         */
        private String lastName;
        
        /**
         * 用户名
         */
        private String userName;
        
        /**
         * 邮箱
         */
        private String email;
    }
}