package top.continew.katai.itl.model.resp;

import lombok.Data;

import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/8/14 15:25
 */
@Data
public class ItlCardTransactionResp {
    /**
     * 实体唯一ID
     * @required
     */
    private String id;

    /**
     * 账户UUID
     * @required
     */
    private String accountId;

    /**
     * 卡UUID
     * @required
     */
    private String cardId;

    /**
     * 卡交易ID
     * @required
     */
    private String cardTransactionId;

    /**
     * 关联卡交易ID
     * @required
     */
    private String relatedCardTransactionId;

    /**
     * 货币代码
     * @required
     */
    private String currency;

    /**
     * 金额
     * @required
     */
    private Double amount;

    /**
     * 交易手续费
     * @required
     */
    private Double fee;

    /**
     * 交易手续费明细
     * @required
     */
    private List<ItlFeeDetail> feeDetails;

    /**
     * 客户端自定义交易ID
     */
    private String clientTransactionId;

    /**
     * 交易类型
     * @required
     * 可能值: Consumption, TransferIn, TransferOut, Credit, Reversal, Frozen, UnFrozen
     */
    private String type;

    /**
     * 交易状态
     * @required
     * 可能值: Pending, Closed, Fail
     */
    private String status;

    /**
     * 商户名称
     * @required
     */
    private String merchantName;

    /**
     * 商户类别代码(MCC)
     * @required
     */
    private String mcc;

    /**
     * MCC类别
     * @required
     */
    private String mccCategory;

    /**
     * 商户城市
     * @required
     */
    private String merchantCity;

    /**
     * 商户国家
     * @required
     */
    private String merchantCountry;

    /**
     * 商户州/省
     * @required
     */
    private String merchantState;

    /**
     * 商户邮编
     * @required
     */
    private String merchantZipcode;

    /**
     * 商户ID (MID)
     * @required
     */
    private String merchantMid;

    /**
     * 订单交易时间（ISO-8601 UTC 日期/时间格式）
     * @required
     */
    private String transactionTime;

    /**
     * 本地交易货币
     * @required
     */
    private String transactionCurrency;

    /**
     * 本地交易金额
     * @required
     */
    private Double transactionAmount;

    /**
     * 交易创建时间（ISO-8601 UTC 日期/时间格式）
     * @required
     */
    private String createTime;

    /**
     * 交易详情 例如: "WALMART.COM 8009666546 BENTONVILLE AR US"
     */
    private String detail;

    /**
     * 交易失败备注 例如: "No sufficient funds"
     */
    private String remark;

    /**
     * 交易手续费明细对象
     */
    @Data
    public static class ItlFeeDetail {
        /**
         * 金额
         * @required
         */
        private Double amount;

        /**
         * 货币
         * @required
         */
        private String currency;

        /**
         * 手续费类型
         * @required
         */
        private String feeType;
    }

}
