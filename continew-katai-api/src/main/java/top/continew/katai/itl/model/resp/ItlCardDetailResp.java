package top.continew.katai.itl.model.resp;

import lombok.Data;

/**
 * @version: 1.00.00
 * @description: 获取卡片详情响应参数
 * @date: 2025/8/14 14:20
 */
@Data
public class ItlCardDetailResp {
    /**
     * 卡的唯一ID
     * 必填
     */
    private String id;

    /**
     * 账户的UUID
     * 必填
     */
    private String accountId;

    /**
     * 卡类型
     * 必填
     * 可能值: "PrepaidCard", "BudgetCard"
     */
    private String type;

    /**
     * 银行识别号（BIN），卡的前6位数字
     * 必填
     */
    private String bin;

    /**
     * 卡的最后4位数字
     * 必填
     */
    private String last4;

    /**
     * 卡的网络
     * 必填
     * 可能值: "VISA", "MasterCard"
     */
    private String network;

    /**
     * 持卡人名字
     * 必填
     */
    private String firstName;

    /**
     * 持卡人姓氏
     * 必填
     */
    private String lastName;

    /**
     * 卡的标签
     */
    private String label;


    /**
     * 知识产权
     */
    private String ipr;

    /**
     * 是否为实体卡
     */
    private Boolean physical;

    /**
     * 卡的状态
     * 必填
     */
    private String status;

    /**
     * 预算的UUID
     */
    private String budgetId;

    /**
     * 创建时间（ISO-8601 UTC 日期/时间格式）
     * 必填
     */
    private String createTime;

    /**
     * 卡的货币余额
     */
    private ItlCardBalance balance;

    /**
     * 无限卡统计（仅当type为BudgetCard时返回）
     */
    private ItlCardStatistics statistics;


    /**
     * 速度控制，限制用户可花费金额
     */
    private ItlCardVelocityControl velocityControl;

    /**
     * 余额对象
     */
    @Data
    public static class ItlCardBalance {
        /**
         * 可用余额
         */
        private String available;

        /**
         * 待处理金额
         */
        private String pending;

        /**
         * 冻结金额
         */
        private String frozen;

        /**
         * 货币代码
         */
        private String currenty;
    }

    /**
     * 统计对象（仅BudgetCard类型返回）
     */
    @Data
    public static class ItlCardStatistics {
        /**
         * 累计消费金额
         * 必填
         */
        private String consumption;

        /**
         * 冲销金额
         * 必填
         */
        private String reversal;

        /**
         * 冲销费
         * 必填
         */
        private String reversalFee;

        /**
         * 退款金额
         * 必填
         */
        private String refund;

        /**
         * 退款费用
         * 必填
         */
        private String refundFee;

        /**
         * 净消耗
         * 必填
         * 计算公式: netConsumption = consumption - (reversal - reversalFee) - (refund - refundFee)
         */
        private String netConsumption;

    }



    /**
     * 速度控制对象
     * 用于限制用户在指定时间段内可花费的最大金额
     */
    @Data
    public static class ItlCardVelocityControl {
        /**
         * 时间段类型
         * 必填
         * 可选值: "DAY", "WEEK", "MONTH", "YEAR", "LIFETIME"
         * 定义限制字段适用的时间段
         */
        private String type;

        /**
         * 限制金额
         * 必填
         * 在类型字段定义的时间段内可以使用的最大货币总和
         */
        private String limit;

    }


}
